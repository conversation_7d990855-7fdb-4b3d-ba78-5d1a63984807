# ==============================================================================
# ==============================================================================
# 2. 导入必要的库
# ==============================================================================
import os
import time as time_module
from datetime import datetime, time, timedelta
import pandas as pd
import google.generativeai as genai
from dotenv import load_dotenv
import warnings

# 抑制akshare相关的弃用警告
warnings.filterwarnings("ignore", message=".*path is deprecated.*", module="akshare.datasets")
warnings.filterwarnings("ignore", category=FutureWarning, module="akshare")

import akshare as ak
import schedule
import logging
import pickle  # 【新增】用于持久化缓存
import re
import threading
import tempfile
import json  # 【新增】用于JSON文件处理
# --- 【新功能】从 get_all_capital_flow_east.py 引入的线程锁 ---
file_lock = threading.Lock()
# --- 【新增】引入概念行业过滤模块 ---
from concept_sector_filter import filter_meaningful_concepts_and_sectors, is_meaningful_concept

# 1. 文件头配置
# ==============================================================================
# 使用的Gemini模型，更新为支持长上下文的预览版
#GEMINI_MODEL_NAME = "gemini-2.5-pro-preview-06-05"

GEMINI_MODEL_NAME = "gemini-2.5-pro"

# 是否打印详细日志，True为开启，False为关闭
ENABLE_DETAILED_LOGGING = True

# 数据获取频率（分钟），每5分钟获取一次
FETCH_INTERVAL_MINUTES = 4

# 是否启用交易时间检查，True= 仅在交易时间执行，False =无视时间限制
ENABLE_TRADING_TIME_CHECK = False

# 任务执行状态控制
TASK_RUNNING = False

# 历史第一名记录 - 个股
HISTORICAL_CHAMPIONS = {
    'open_to_now': [],      # 开盘至今的历史第一名
    '60min': [],            # 60分钟内的历史第一名
    '15min': []             # 15分钟内的历史第一名
}

# 历史第一名记录 - 行业
HISTORICAL_SECTOR_CHAMPIONS = {
    'open_to_now': [],      # 开盘至今的行业历史第一名
    '60min': [],            # 60分钟内的行业历史第一名
    '15min': []             # 15分钟内的行业历史第一名
}

# 历史第一名记录 - 概念
HISTORICAL_CONCEPT_CHAMPIONS = {
    'open_to_now': [],      # 开盘至今的概念历史第一名
    '60min': [],            # 60分钟内的概念历史第一名
    '15min': []             # 15分钟内的概念历史第一名
}

# 记录上次清理时间
LAST_CLEANUP_TIME = None

# ==============================================================================
# 模块一：多时间点数据缓存系统 (The Memory)
# ==============================================================================

# 定义缓存文件路径
DATA_CACHE_FILE = "multi_timescale_cache.pkl"



# 加载 ..env 文件中的环境变量
load_dotenv()


# 定义使用的API密钥列表，从环境变量中加载，允许动态扩展（如GEMINI_API_KEY2, GEMINI_API_KEY3等）
API_KEYS = [f"GEMINI_API_KEY{i}" for i in range(2, 8) if f"GEMINI_API_KEY{i}" in os.environ]# API密钥轮询索引（全局变量，用于跟踪当前使用的密钥）
API_KEY_INDEX_FILE = "gemini_api_key_index.txt"
try:
    with open(API_KEY_INDEX_FILE, 'r') as f:
        API_KEY_INDEX = int(f.read().strip())
except (FileNotFoundError, ValueError):
    API_KEY_INDEX = 0
# 确保 API_KEY_INDEX 在有效范围内
API_KEY_INDEX = API_KEY_INDEX % len(API_KEYS) if API_KEYS else 0
# 检查API Key是否存在
if not API_KEYS:
    logging.error("错误：未找到任何 GEMINI_API_KEY。请在 ..env 文件中设置至少一个有效的 GEMINI_API_KEY（例如 GEMINI_API_KEY2）。")
    exit()



# ==============================================================================
# 3. 初始化和配置加载
# ==============================================================================

# 设置日志
if ENABLE_DETAILED_LOGGING:
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
else:
    logging.basicConfig(level=logging.WARNING, format='%(asctime)s - %(levelname)s - %(message)s')





# 获取当前轮询的API密钥
def get_current_api_key():
    global API_KEY_INDEX
    api_key_name = API_KEYS[API_KEY_INDEX]
    api_key = os.getenv(api_key_name)
    # 更新索引以轮询下一个密钥
    API_KEY_INDEX = (API_KEY_INDEX + 1) % len(API_KEYS)
    # 保存 API_KEY_INDEX 到文件
    with open(API_KEY_INDEX_FILE, 'w') as f:
        f.write(str(API_KEY_INDEX))
    logging.info(f"当前使用API密钥: {api_key_name}")
    return api_key

# 检查API Key是否存在
API_KEY = get_current_api_key()
if not API_KEY:
    logging.error(f"错误：未找到 {API_KEYS[API_KEY_INDEX-1]}。请在 ..env 文件中设置。")
    exit()

# 配置代理 (如果启用)
USE_PROXY = os.getenv("USE_PROXY", 'True').lower() in ('true', '1', 't')
PROXY_URL = os.getenv("PROXY_URL")
if USE_PROXY and PROXY_URL:
    os.environ['http_proxy'] = PROXY_URL
    os.environ['https_proxy'] = PROXY_URL
    logging.info(f"已配置代理: {PROXY_URL}")

# ==============================================================================
# 数值格式化工具函数
# ==============================================================================

def format_amount(value):
    """
    格式化金额数值，将科学计数法转换为易读格式
    :param value: 数值
    :return: 格式化后的字符串
    """
    try:
        if pd.isna(value) or value is None:
            return "0"
        
        num_value = float(value)
        
        if abs(num_value) >= 1e8:  # 亿级别
            return f"{num_value/1e8:.2f}亿"
        elif abs(num_value) >= 1e4:  # 万级别
            return f"{num_value/1e4:.2f}万"
        else:
            return f"{num_value:.2f}"
            
    except (ValueError, TypeError):
        return str(value)


def format_percentage(value):
    """
    格式化百分比数值
    :param value: 数值
    :return: 格式化后的字符串
    """
    try:
        if pd.isna(value) or value is None:
            return "0.00%"
        
        num_value = float(value)
        return f"{num_value:.2f}%"
        
    except (ValueError, TypeError):
        return str(value)


def format_dataframe_for_display(df, amount_columns=None, percentage_columns=None):
    """
    格式化DataFrame用于显示，处理科学计数法和单位问题
    :param df: 原始DataFrame
    :param amount_columns: 需要格式化为金额的列名列表
    :param percentage_columns: 需要格式化为百分比的列名列表
    :return: 格式化后的DataFrame
    """
    if df is None or df.empty:
        return df
    
    formatted_df = df.copy()
    
    # 默认的金额列名模式
    if amount_columns is None:
        amount_columns = []
        for col in formatted_df.columns:
            col_lower = str(col).lower()
            # 扩展金额关键词，包含更多可能的列名
            amount_keywords = ['净流入', '净额', '成交额', '资金', '流入', '流出', '金额', '净流', '主力', '超大单', '大单', '中单', '小单', '成交量', '市值']
            if any(keyword in col_lower for keyword in amount_keywords):
                if '占比' not in col_lower and '%' not in col_lower and '比率' not in col_lower:  # 排除百分比列
                    amount_columns.append(col)
    
    # 默认的百分比列名模式
    if percentage_columns is None:
        percentage_columns = []
        for col in formatted_df.columns:
            col_lower = str(col).lower()
            # 扩展百分比关键词
            percentage_keywords = ['占比', '涨跌幅', '涨幅', '跌幅', '比率', '百分比', '率']
            if any(keyword in col_lower for keyword in percentage_keywords) or '%' in col_lower:
                percentage_columns.append(col)
    
    # 调试日志：记录格式化的列
    if amount_columns:
        logging.debug(f"格式化金额列: {amount_columns}")
    if percentage_columns:
        logging.debug(f"格式化百分比列: {percentage_columns}")
    
    # 格式化金额列
    for col in amount_columns:
        if col in formatted_df.columns:
            formatted_df[col] = formatted_df[col].apply(format_amount)
    
    # 格式化百分比列
    for col in percentage_columns:
        if col in formatted_df.columns:
            formatted_df[col] = formatted_df[col].apply(format_percentage)
    
    return formatted_df


# 配置Gemini
try:
    genai.configure(api_key=API_KEY)
    logging.info("Gemini API 配置成功。")
except Exception as e:
    logging.error(f"Gemini API 配置失败: {e}")
    exit()

# ==============================================================================
# 4. 功能函数定义
# ==============================================================================

def manage_cache(new_data_dict):
    """
    模块一：缓存管理函数
    在job()执行的末尾被调用，接收包含当前所有新数据的字典new_data_dict。

    :param new_data_dict: 包含当前所有新数据的字典
    """
    try:
        # 加载现有缓存
        cache = {'open': None, 'history': []}
        if os.path.exists(DATA_CACHE_FILE):
            try:
                with open(DATA_CACHE_FILE, 'rb') as f:
                    cache = pickle.load(f)
                logging.info(f"成功加载现有缓存，历史记录数量: {len(cache.get('history', []))}")
            except Exception as e:
                logging.error(f"加载缓存文件失败: {e}，将初始化新缓存")
                cache = {'open': None, 'history': []}

        # 处理开盘数据
        if cache['open'] is None:
            cache['open'] = new_data_dict.copy()
            logging.info("这是当日首次成功运行，已保存开盘数据")

        # 更新历史数据
        current_time = datetime.now()
        cache['history'].append((current_time, new_data_dict.copy()))
        logging.info(f"已添加新的历史记录，时间戳: {current_time}")

        # 修剪历史数据：删除60分钟之前的记录
        cutoff_time = current_time - timedelta(minutes=60)
        original_count = len(cache['history'])
        cache['history'] = [(timestamp, data) for timestamp, data in cache['history']
                           if timestamp >= cutoff_time]
        removed_count = original_count - len(cache['history'])
        if removed_count > 0:
            logging.info(f"已清理 {removed_count} 条过期历史记录（超过60分钟）")

        # 保存缓存
        with open(DATA_CACHE_FILE, 'wb') as f:
            pickle.dump(cache, f)
        logging.info(f"缓存已更新并保存，当前历史记录数量: {len(cache['history'])}")

    except Exception as e:
        logging.error(f"缓存管理过程中发生错误: {e}")


def get_data_from_cache(cache, minutes_ago):
    """
    模块一：数据提取函数
    从加载的缓存中获取特定时间点的数据。

    :param cache: 缓存字典
    :param minutes_ago: 需要回溯的分钟数 (例如 0, 15, 30。0代表最近一次)
    :return: 对应时间点的DataFrame字典，如果没有找到则返回None
    """
    try:
        if not cache or 'history' not in cache:
            logging.warning("缓存为空或格式不正确")
            return None

        if not cache['history']:
            logging.warning("历史记录为空")
            return None

        target_time = datetime.now() - timedelta(minutes=minutes_ago)

        # 如果minutes_ago为0，返回最近一次的数据
        if minutes_ago == 0:
            latest_record = cache['history'][-1]
            logging.info(f"返回最近一次数据，时间戳: {latest_record[0]}")
            return latest_record[1]

        # 寻找最接近目标时间的记录
        best_match = None
        min_time_diff = float('inf')

        for timestamp, data in cache['history']:
            time_diff = abs((timestamp - target_time).total_seconds())
            if time_diff < min_time_diff:
                min_time_diff = time_diff
                best_match = (timestamp, data)

        if best_match:
            logging.info(f"找到 {minutes_ago} 分钟前的最佳匹配数据，时间戳: {best_match[0]}，时间差: {min_time_diff/60:.1f}分钟")
            return best_match[1]
        else:
            logging.warning(f"未找到 {minutes_ago} 分钟前的数据")
            return None

    except Exception as e:
        logging.error(f"从缓存获取数据时发生错误: {e}")
        return None


# ==============================================================================
# 模块二：高级动态分析报告引擎 (The Delta Engine)
# ==============================================================================

def cleanup_historical_champions():
    """
    清理历史第一名记录，按时间周期进行适当的清理
    - 15分钟：保留最近2小时内的记录
    - 60分钟：保留最近8小时内的记录
    - 开盘至今：每日开盘时清空
    """
    global HISTORICAL_CHAMPIONS, HISTORICAL_SECTOR_CHAMPIONS, HISTORICAL_CONCEPT_CHAMPIONS, LAST_CLEANUP_TIME
    
    current_time = datetime.now()
    current_date = current_time.date()
    
    try:
        # 检查是否需要每日清理
        if LAST_CLEANUP_TIME is None or LAST_CLEANUP_TIME.date() != current_date:
            # 新的一天，清空开盘至今的记录
            HISTORICAL_CHAMPIONS['open_to_now'] = []
            HISTORICAL_SECTOR_CHAMPIONS['open_to_now'] = []
            HISTORICAL_CONCEPT_CHAMPIONS['open_to_now'] = []
            LAST_CLEANUP_TIME = current_time
            logging.info("每日清理：已清空开盘至今历史第一名记录（个股、行业、概念）")
        
        # 定义清理函数
        def clean_records(champions_dict, record_type):
            # 清理15分钟记录（保留最近2小时）
            cutoff_15min = current_time - timedelta(hours=2)
            original_count_15min = len(champions_dict['15min'])
            champions_dict['15min'] = [c for c in champions_dict['15min'] if c['time'] >= cutoff_15min]
            cleaned_count_15min = original_count_15min - len(champions_dict['15min'])
            
            # 清理60分钟记录（保留最近8小时）
            cutoff_60min = current_time - timedelta(hours=8)
            original_count_60min = len(champions_dict['60min'])
            champions_dict['60min'] = [c for c in champions_dict['60min'] if c['time'] >= cutoff_60min]
            cleaned_count_60min = original_count_60min - len(champions_dict['60min'])
            
            return cleaned_count_15min, cleaned_count_60min
        
        # 清理个股、行业、概念记录
        stock_15min, stock_60min = clean_records(HISTORICAL_CHAMPIONS, "个股")
        sector_15min, sector_60min = clean_records(HISTORICAL_SECTOR_CHAMPIONS, "行业")
        concept_15min, concept_60min = clean_records(HISTORICAL_CONCEPT_CHAMPIONS, "概念")
        
        total_cleaned = stock_15min + stock_60min + sector_15min + sector_60min + concept_15min + concept_60min
        if total_cleaned > 0:
            logging.info(f"历史第一名记录清理完成：个股({stock_15min}+{stock_60min}), 行业({sector_15min}+{sector_60min}), 概念({concept_15min}+{concept_60min})")
            
    except Exception as e:
        logging.error(f"清理历史第一名记录时发生错误: {e}")


def analyze_funding_gap_with_limit_up(df_data, data_type="板块", limit_up_data=None):
    """
    增强版资金断层分析函数，结合涨停数据进行综合判断
    """
    try:
        # 检查DataFrame是否为空或缺少必要列
        if df_data is None or df_data.empty:
            return ""
        
        # 自适应列名检测
        flow_col = None
        name_col = None
        
        for col in ['主力净流入', '今日主力净流入-净额', '净流入', '资金净流入']:
            if col in df_data.columns:
                flow_col = col
                break
                
        for col in ['板块名称', '名称', '股票名称', '概念', '行业']:
            if col in df_data.columns:
                name_col = col
                break
        
        if flow_col is None or name_col is None:
            return ""
        
        # 1. 数据预处理 - 确保数值型
        df_copy = df_data.copy()
        df_copy[flow_col] = pd.to_numeric(df_copy[flow_col], errors='coerce').fillna(0)
        
        positive_flow_df = df_copy[df_copy[flow_col] > 0]
        if len(positive_flow_df) < 3:
            return ""  # 数据不足，返回空字符串
        
        # 取前10名进行分析
        top_data = positive_flow_df.head(10)
        inflows = top_data[flow_col].tolist()
        names = top_data[name_col].tolist()
        
        if len(inflows) < 2:
            return ""
        
        # 2. 结合涨停数据进行增强分析
        limit_up_boost = 1.0  # 涨停加成系数
        if limit_up_data is not None and not limit_up_data.empty:
            # 统计首位板块/个股的涨停情况
            top_name = names[0]
            # 检查首位是否有相关涨停股
            if '名称' in limit_up_data.columns:
                related_limit_ups = 0
                for _, limit_stock in limit_up_data.head(50).iterrows():  # 只检查前50只涨停股
                    stock_name = limit_stock['名称']
                    # 简单关键词匹配（如果是个股，直接匹配名称）
                    if data_type == "个股" and stock_name == top_name:
                        related_limit_ups += 1
                        break
                    # 如果是板块，检查是否包含板块关键词
                    elif data_type in ["板块", "行业", "概念"] and any(keyword in stock_name for keyword in top_name.split()):
                        related_limit_ups += 1
                
                if related_limit_ups > 0:
                    limit_up_boost = 1.0 + min(related_limit_ups * 0.1, 0.5)  # 最多50%加成
        
        # 3. 计算断层比例（结合涨停加成）
        adjusted_top_ratio = (inflows[0] / inflows[1] if inflows[1] > 0 else float('inf')) * limit_up_boost
        top_ratio = inflows[0] / inflows[1] if inflows[1] > 0 else float('inf')
        
        # 4. 断层阈值判断（1.5倍为断层线）
        gap_threshold = 1.5
        if adjusted_top_ratio < gap_threshold:
            return ""
        
        # 5. 计算上下文比例（2-10名的最大差距）
        context_ratios = []
        for i in range(1, min(len(inflows) - 1, 9)):
            if inflows[i + 1] > 0:
                context_ratios.append(inflows[i] / inflows[i + 1])
        
        max_context_ratio = max(context_ratios) if context_ratios else 1.0
        
        # 6. 最终断层判定（龙头优势需要显著大于背景差距）
        is_significant_gap = adjusted_top_ratio > max_context_ratio * 1.2
        
        if is_significant_gap:
            # 判断是否为单龙头断层还是集团断层
            if len(inflows) >= 3 and inflows[1] / inflows[2] > 1.3:
                gap_type = f"{data_type}双龙头断层"
                leader_desc = f"【{names[0]}】、【{names[1]}】"
            else:
                gap_type = f"{data_type}单龙头断层"
                leader_desc = f"【{names[0]}】"
            
            # 添加涨停加成信息
            boost_info = f"（涨停加成{limit_up_boost:.1f}倍）" if limit_up_boost > 1.0 else ""
            
            return (f"**{data_type}资金断层**: {gap_type}，断层龙头{leader_desc}，"
                   f"领先优势{top_ratio:.1f}倍{boost_info}（显著超越背景差距{max_context_ratio:.1f}倍）")
        
    except Exception as e:
        logging.error(f"增强版资金断层分析失败: {e}")
        return ""
    
    return ""


def generate_dynamic_fund_flow_report(new_df, cache_open, cache_15min, cache_30min, cache_60min, cache_latest):
    """
    模块二：核心报告生成函数
    精确实现用户设计的、能够生成多维度对比报告的函数。

    :param new_df: 最新的个股资金流DataFrame
    :param cache_open: 开盘时的DataFrame
    :param cache_15min: 15分钟前的DataFrame
    :param cache_30min: 30分钟前的DataFrame
    :param cache_60min: 60分钟前的DataFrame
    :param cache_latest: 上一次运行的DataFrame
    :return: 格式化的动态报告文本
    """
    global HISTORICAL_CHAMPIONS
    
    try:
        # 首先清理过期的历史记录
        cleanup_historical_champions()
        
        if new_df is None or new_df.empty:
            return "无法生成动态报告：当前数据为空"

        # 步骤1: 数据预处理
        # 只取前100条记录
        new_df_top100 = new_df.head(100).copy()

        # 为新数据创建排名列
        new_df_top100['排名_新'] = range(1, len(new_df_top100) + 1)

        # 确定股票代码列名
        code_column = None
        for col in ['股票代码', '代码', 'code']:
            if col in new_df_top100.columns:
                code_column = col
                break

        if code_column is None:
            return "无法生成动态报告：未找到股票代码列"

        # 确定股票名称列名
        name_column = None
        for col in ['名称', '股票名称', '股票简称', 'name']:
            if col in new_df_top100.columns:
                name_column = col
                break

        if name_column is None:
            name_column = code_column  # 如果找不到名称列，使用代码列

        # 步骤1.5: 记录历史第一名信息
        current_time = datetime.now()
        current_champion_info = {
            'time': current_time,
            'code': new_df_top100.iloc[0][code_column],
            'name': new_df_top100.iloc[0][name_column],
            'amount': new_df_top100.iloc[0].get('今日主力净流入-净额', 0) if '今日主力净流入-净额' in new_df_top100.columns else 0
        }

        # 更新不同时间周期的历史第一名记录
        def update_champions(period_key, cache_data):
            if cache_data is not None:
                cache_df = None
                if isinstance(cache_data, dict) and 'fund_flow_df' in cache_data:
                    cache_df = cache_data['fund_flow_df']
                elif isinstance(cache_data, pd.DataFrame):
                    cache_df = cache_data

                if cache_df is not None and not cache_df.empty and code_column in cache_df.columns:
                    # 检查当前第一名是否与缓存第一名不同
                    cache_champion_code = cache_df.iloc[0][code_column]
                    if cache_champion_code != current_champion_info['code']:
                        # 添加到历史记录
                        champion_exists = any(c['code'] == current_champion_info['code'] for c in HISTORICAL_CHAMPIONS[period_key])
                        if not champion_exists:
                            HISTORICAL_CHAMPIONS[period_key].append(current_champion_info.copy())
                            logging.info(f"记录{period_key}历史第一名: {current_champion_info['name']}({current_champion_info['code']})")

        # 更新各时间周期的历史第一名
        update_champions('15min', cache_15min)
        update_champions('60min', cache_60min)
        update_champions('open_to_now', cache_open)

        # 当前第一名也加入记录（确保不重复）
        for period_key in ['15min', '60min', 'open_to_now']:
            champion_exists = any(c['code'] == current_champion_info['code'] for c in HISTORICAL_CHAMPIONS[period_key])
            if not champion_exists:
                HISTORICAL_CHAMPIONS[period_key].append(current_champion_info.copy())

        # 步骤2: 构建超级宽表
        result_df = new_df_top100.copy()

        # 为各个缓存数据创建排名并合并
        cache_data_list = [
            (cache_latest, '排名_上次'),
            (cache_15min, '排名_15分前'),
            (cache_30min, '排名_30分前'),
            (cache_60min, '排名_60分前'),
            (cache_open, '排名_开盘')
        ]

        for cache_data, rank_col_name in cache_data_list:
            if cache_data is not None:
                # 处理不同的缓存数据结构
                cache_df = None
                if isinstance(cache_data, dict) and 'fund_flow_df' in cache_data:
                    cache_df = cache_data['fund_flow_df']
                elif isinstance(cache_data, pd.DataFrame):
                    cache_df = cache_data

                if cache_df is not None and not cache_df.empty:
                    # 创建排名列
                    cache_df_ranked = cache_df.head(100).copy()
                    cache_df_ranked[rank_col_name] = range(1, len(cache_df_ranked) + 1)

                    # 只保留代码和排名列进行合并
                    if code_column in cache_df_ranked.columns:
                        merge_df = cache_df_ranked[[code_column, rank_col_name]]
                        # 合并到结果DataFrame
                        result_df = pd.merge(result_df, merge_df, on=code_column, how='left')
                    else:
                        # 如果缓存数据中没有对应的代码列，创建空的排名列
                        result_df[rank_col_name] = None
                else:
                    # 如果缓存数据为空，创建空的排名列
                    result_df[rank_col_name] = None
            else:
                # 如果缓存数据为空，创建空的排名列
                result_df[rank_col_name] = None

        # 步骤3: 计算"排名变化"列
        # 先记录哪些列有有效的历史数据
        valid_history_cols = []
        for col in ['排名_上次', '排名_15分前', '排名_30分前', '排名_60分前', '排名_开盘']:
            if col in result_df.columns:
                # 检查该列是否有有效数据（不全是NaN）
                if not result_df[col].isna().all():
                    valid_history_cols.append(col)
                    # 只对有部分有效数据的列使用fillna(999)，999表示该股票在历史时点不在榜单中
                    result_df[col] = result_df[col].fillna(999)
                else:
                    # 如果该列全是NaN，说明没有历史数据，标记为-1表示无历史数据
                    result_df[col] = -1

        # 计算排名变化（正值表示排名上升，负值表示排名下降）
        # 只有当历史排名不是-1（有历史数据）且不是999（该股票当时在榜单中）时才计算变化
        for col, change_col in [
            ('排名_上次', '排名变化_vs_上次'),
            ('排名_15分前', '排名变化_vs_15分'),
            ('排名_30分前', '排名变化_vs_30分'),
            ('排名_60分前', '排名变化_vs_60分'),
            ('排名_开盘', '排名变化_vs_开盘')
        ]:
            if col in result_df.columns:
                # 创建排名变化列
                result_df[change_col] = result_df.apply(
                    lambda row: (
                        row[col] - row['排名_新'] if row[col] not in [-1, 999]
                        else None
                    ), axis=1
                )

        # 步骤4: 生成格式化的报告文本

        # a. 生成"核心决策摘要"
        # 找出当前排名第一的股票（日内总龙头）
        name_column = None
        for col in ['名称', '股票名称', '股票简称', 'name']:
            if col in result_df.columns:
                name_column = col
                break

        if name_column is None:
            name_column = code_column  # 如果找不到名称列，使用代码列

        top_stock = result_df.iloc[0]
        top_stock_name = top_stock[name_column]
        top_stock_code = top_stock[code_column]

        # 生成历史第一名黑马显示
        def generate_champions_text(period_key, period_name):
            champions = HISTORICAL_CHAMPIONS[period_key]
            if not champions:
                return f"**{period_name}最强黑马**: 暂无历史记录，当前为首次运行或数据重置"
            
            # 按时间排序，最新的在前
            sorted_champions = sorted(champions, key=lambda x: x['time'], reverse=True)
            
            # 去重，只保留每个股票的最新记录
            unique_champions = {}
            for champion in sorted_champions:
                code = champion['code']
                if code not in unique_champions:
                    unique_champions[code] = champion
            
            unique_list = list(unique_champions.values())
            
            if len(unique_list) == 1:
                # 只有一个第一名
                champion = unique_list[0]
                time_str = champion['time'].strftime('%H:%M')
                return f"**{period_name}最强黑马**: {champion['name']}({champion['code']}) - {time_str}登顶第1位，{period_name}内持续占据资金流制高点"
            else:
                # 多个第一名，显示最多前5名
                top_champions = unique_list[:5]
                
                # 格式化显示每个冠军的登顶时间
                champion_details = []
                for champion in top_champions:
                    time_str = champion['time'].strftime('%H:%M')
                    champion_details.append(f"{champion['name']}({champion['code']}, {time_str})")
                
                total_count = len(unique_list)
                if total_count <= 5:
                    champions_str = ', '.join(champion_details)
                    return f"**{period_name}最强黑马**: {champions_str} - {period_name}内共{total_count}只股票轮流登顶，市场资金流向轮动活跃"
                else:
                    champions_str = ', '.join(champion_details)
                    return f"**{period_name}最强黑马**: {champions_str}等{total_count}只 - {period_name}内多股轮流登顶，资金流向高度活跃"

        # 15分钟历史第一名
        black_horse_15min_text = generate_champions_text('15min', '15分钟')

        # 60分钟历史第一名
        black_horse_60min_text = generate_champions_text('60min', '60分钟')

        # 开盘至今历史第一名
        black_horse_open_text = generate_champions_text('open_to_now', '开盘至今')

        # 判断市场节奏 - 基于历史第一名的数量和活跃度
        total_champions = len(HISTORICAL_CHAMPIONS['15min']) + len(HISTORICAL_CHAMPIONS['60min']) + len(HISTORICAL_CHAMPIONS['open_to_now'])
        avg_champions = total_champions / 3 if total_champions > 0 else 0
        
        if avg_champions > 3:
            market_rhythm = '加速上攻'
        elif avg_champions > 1:
            market_rhythm = '稳步推进'
        else:
            market_rhythm = '震荡整理'

        # 格式化核心决策摘要
        if black_horse_15min_text or black_horse_60min_text or black_horse_open_text:
            summary_text = f"""
## 🎯 核心决策摘要

**日内总龙头**: {top_stock_name}({top_stock_code}) - 当前资金流排名第1位，市场资金的绝对焦点

{black_horse_15min_text}

{black_horse_60min_text}

{black_horse_open_text}

**市场节奏**: 基于多时间维度分析，当前市场呈现{market_rhythm}态势
"""
        else:
            # 没有有效的历史数据时的处理
            summary_text = f"""
## 🎯 核心决策摘要

**日内总龙头**: {top_stock_name}({top_stock_code}) - 当前资金流排名第1位，市场资金的绝对焦点

**数据状态**: 当前为首次运行或收盘后数据，暂无历史时点对比数据

**市场节奏**: 基于当前资金流排名，市场呈现资金集中态势，建议关注排名靠前的活跃标的
"""

        # b. 生成"关键变化看板" (Markdown表格)
        # 筛选出用于展示的核心列
        display_columns = [name_column, code_column, '排名_新']
        change_columns = ['排名变化_vs_上次', '排名变化_vs_15分', '排名变化_vs_30分', '排名变化_vs_60分', '排名变化_vs_开盘']

        # 创建显示用的DataFrame
        display_df = result_df[display_columns + change_columns].head(20).copy()

        # 将排名变化数值格式化为带箭头的字符串
        def format_change(value):
            if pd.isna(value):
                return "新上榜"  # 无历史数据，表示新上榜
            elif value == 0:
                return "→0"
            elif value > 0:
                return f"↑{int(value)}"
            else:
                return f"↓{int(abs(value))}"

        for col in change_columns:
            display_df[col] = display_df[col].apply(format_change)

        # 重命名列以便显示
        display_df.columns = ['股票名称', '代码', '当前排名', 'vs上次', 'vs15分前', 'vs30分前', 'vs60分前', 'vs开盘']

        # 转换为Markdown表格
        markdown_table = display_df.to_markdown(index=False)

        # c. 个股资金流断层分析
        stock_gap_analysis = analyze_funding_gap(
            pd.DataFrame({
                '板块名称': result_df[name_column].head(20),
                '主力净流入': result_df['今日主力净流入-净额'].head(20) if '今日主力净流入-净额' in result_df.columns else result_df.iloc[:, -1].head(20)  # 使用最后一列作为资金流入
            }),
            "个股"
        )
        
        # 在表格前添加断层分析
        gap_section = f"\n### 💎 个股资金流断层分析\n{stock_gap_analysis}\n" if stock_gap_analysis else ""
        
        # c. 组合最终报告
        final_report = f"""{summary_text}
{gap_section}
## 📊 关键变化看板 (前20名)

{markdown_table}

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*数据说明: ↑表示排名上升，↓表示排名下降，→表示无变化，"新上榜"表示首次出现或无历史对比数据*
"""

        return final_report

    except Exception as e:
        logging.error(f"生成动态资金流报告时发生错误: {e}")
        return f"动态报告生成失败: {str(e)}"


def analyze_funding_gap(df_data, data_type="板块"):
    """
    核心资金断层分析函数，提取自dynamic_gap_detector.py
    """
    try:
        # 检查DataFrame是否为空或缺少必要列
        if df_data is None or df_data.empty:
            return ""
        
        # 自适应列名检测
        flow_col = None
        name_col = None
        
        for col in ['主力净流入', '今日主力净流入-净额', '净流入', '资金净流入']:
            if col in df_data.columns:
                flow_col = col
                break
                
        for col in ['板块名称', '名称', '股票名称', '概念', '行业']:
            if col in df_data.columns:
                name_col = col
                break
        
        if flow_col is None or name_col is None:
            return ""
        
        # 1. 数据预处理 - 确保数值型
        df_copy = df_data.copy()
        df_copy[flow_col] = pd.to_numeric(df_copy[flow_col], errors='coerce').fillna(0)
        
        positive_flow_df = df_copy[df_copy[flow_col] > 0]
        if len(positive_flow_df) < 3:
            return ""  # 数据不足，返回空字符串
        
        # 取前10名进行分析
        top_data = positive_flow_df.head(10)
        inflows = top_data[flow_col].tolist()
        names = top_data[name_col].tolist()
        
        if len(inflows) < 2:
            return ""
        
        # 2. 计算断层比例
        top_ratio = inflows[0] / inflows[1] if inflows[1] > 0 else float('inf')
        
        # 3. 断层阈值判断（1.5倍为断层线）
        gap_threshold = 1.5
        if top_ratio < gap_threshold:
            return ""
        
        # 4. 计算上下文比例（2-10名的最大差距）
        context_ratios = []
        for i in range(1, min(len(inflows) - 1, 9)):
            if inflows[i + 1] > 0:
                context_ratios.append(inflows[i] / inflows[i + 1])
        
        max_context_ratio = max(context_ratios) if context_ratios else 1.0
        
        # 5. 最终断层判定（龙头优势需要显著大于背景差距）
        is_significant_gap = top_ratio > max_context_ratio * 1.2
        
        if is_significant_gap:
            # 判断是否为单龙头断层还是集团断层
            if len(inflows) >= 3 and inflows[1] / inflows[2] > 1.3:
                gap_type = f"{data_type}双龙头断层"
                leader_desc = f"【{names[0]}】、【{names[1]}】"
            else:
                gap_type = f"{data_type}单龙头断层"
                leader_desc = f"【{names[0]}】"
            
            return (f"**{data_type}资金断层**: {gap_type}，断层龙头{leader_desc}，"
                   f"领先优势{top_ratio:.1f}倍（显著超越背景差距{max_context_ratio:.1f}倍）")
        
    except Exception as e:
        logging.error(f"资金断层分析失败: {e}")
        return ""
    
    return ""


def generate_dynamic_sector_concept_report(new_sector_df, new_concept_df, cache_open, cache_15min, cache_30min, cache_60min, cache_latest, individual_stock_df):
    """
    生成行业与概念资金流动态对比报告

    :param new_sector_df: 最新的行业资金流DataFrame
    :param new_concept_df: 最新的概念资金流DataFrame
    :param cache_open: 开盘时的缓存数据
    :param cache_15min: 15分钟前的缓存数据
    :param cache_30min: 30分钟前的缓存数据
    :param cache_60min: 60分钟前的缓存数据
    :param cache_latest: 上一次运行的缓存数据
    :param individual_stock_df: 个股资金流数据，用于交叉验证领涨股
    :return: 格式化的行业概念动态报告文本
    """
    global HISTORICAL_SECTOR_CHAMPIONS, HISTORICAL_CONCEPT_CHAMPIONS
    
    try:
        # 首先清理过期的历史记录（这个操作已经在个股部分完成了，这里不重复）
        
        # 记录当前行业和概念第一名
        current_time = datetime.now()
        
        # 记录行业第一名
        def record_sector_champion():
            if new_sector_df is not None and not new_sector_df.empty:
                # 寻找行业名称列
                name_col = None
                for col in ['行业', '板块', '名称', '行业名称', '板块名称']:
                    if col in new_sector_df.columns:
                        name_col = col
                        break
                
                # 寻找资金流列
                flow_col = None
                for col in ['主力净流入', '净流入', '资金净流入', '今日主力净流入', '主力资金净流入']:
                    if col in new_sector_df.columns:
                        flow_col = col
                        break
                
                if name_col and flow_col:
                    sector_champion_info = {
                        'time': current_time,
                        'name': new_sector_df.iloc[0][name_col],
                        'amount': new_sector_df.iloc[0][flow_col] if pd.notna(new_sector_df.iloc[0][flow_col]) else 0
                    }
                    
                    # 检查各个时间周期的缓存数据，更新历史记录
                    def update_sector_champions(period_key, cache_data):
                        if cache_data is not None:
                            cache_sector_df = None
                            if isinstance(cache_data, dict) and 'sector_fund_flow_df' in cache_data:
                                cache_sector_df = cache_data['sector_fund_flow_df']
                            
                            # 检查缓存中的第一名是否与当前不同
                            if cache_sector_df is not None and not cache_sector_df.empty and name_col in cache_sector_df.columns:
                                cache_champion_name = cache_sector_df.iloc[0][name_col]
                                if cache_champion_name != sector_champion_info['name']:
                                    # 添加到历史记录
                                    champion_exists = any(c['name'] == sector_champion_info['name'] for c in HISTORICAL_SECTOR_CHAMPIONS[period_key])
                                    if not champion_exists:
                                        HISTORICAL_SECTOR_CHAMPIONS[period_key].append(sector_champion_info.copy())
                                        logging.info(f"记录{period_key}行业历史第一名: {sector_champion_info['name']}")
                    
                    # 更新各时间周期的历史第一名
                    update_sector_champions('15min', cache_15min)
                    update_sector_champions('60min', cache_60min)
                    update_sector_champions('open_to_now', cache_open)
                    
                    # 当前第一名也加入记录（确保不重复）
                    for period_key in ['15min', '60min', 'open_to_now']:
                        champion_exists = any(c['name'] == sector_champion_info['name'] for c in HISTORICAL_SECTOR_CHAMPIONS[period_key])
                        if not champion_exists:
                            HISTORICAL_SECTOR_CHAMPIONS[period_key].append(sector_champion_info.copy())
        
        # 记录概念第一名
        def record_concept_champion():
            if new_concept_df is not None and not new_concept_df.empty:
                # 寻找概念名称列
                name_col = None
                for col in ['概念', '板块', '名称', '概念名称', '板块名称']:
                    if col in new_concept_df.columns:
                        name_col = col
                        break
                
                # 寻找资金流列
                flow_col = None
                for col in ['主力净流入', '净流入', '资金净流入', '今日主力净流入', '主力资金净流入']:
                    if col in new_concept_df.columns:
                        flow_col = col
                        break
                
                if name_col and flow_col:
                    concept_champion_info = {
                        'time': current_time,
                        'name': new_concept_df.iloc[0][name_col],
                        'amount': new_concept_df.iloc[0][flow_col] if pd.notna(new_concept_df.iloc[0][flow_col]) else 0
                    }
                    
                    # 检查各个时间周期的缓存数据，更新历史记录
                    def update_concept_champions(period_key, cache_data):
                        if cache_data is not None:
                            cache_concept_df = None
                            if isinstance(cache_data, dict) and 'concept_fund_flow_df' in cache_data:
                                cache_concept_df = cache_data['concept_fund_flow_df']
                            
                            # 检查缓存中的第一名是否与当前不同
                            if cache_concept_df is not None and not cache_concept_df.empty and name_col in cache_concept_df.columns:
                                cache_champion_name = cache_concept_df.iloc[0][name_col]
                                if cache_champion_name != concept_champion_info['name']:
                                    # 添加到历史记录
                                    champion_exists = any(c['name'] == concept_champion_info['name'] for c in HISTORICAL_CONCEPT_CHAMPIONS[period_key])
                                    if not champion_exists:
                                        HISTORICAL_CONCEPT_CHAMPIONS[period_key].append(concept_champion_info.copy())
                                        logging.info(f"记录{period_key}概念历史第一名: {concept_champion_info['name']}")
                    
                    # 更新各时间周期的历史第一名
                    update_concept_champions('15min', cache_15min)
                    update_concept_champions('60min', cache_60min)
                    update_concept_champions('open_to_now', cache_open)
                    
                    # 当前第一名也加入记录（确保不重复）
                    for period_key in ['15min', '60min', 'open_to_now']:
                        champion_exists = any(c['name'] == concept_champion_info['name'] for c in HISTORICAL_CONCEPT_CHAMPIONS[period_key])
                        if not champion_exists:
                            HISTORICAL_CONCEPT_CHAMPIONS[period_key].append(concept_champion_info.copy())
        
        # 执行记录操作
        record_sector_champion()
        record_concept_champion()
        
        # 数据预处理和标准化 (最关键步骤)
        logging.info(f"开始生成行业概念动态报告...")
        logging.info(f"行业数据状态: {new_sector_df is not None and not new_sector_df.empty if new_sector_df is not None else False}")
        logging.info(f"概念数据状态: {new_concept_df is not None and not new_concept_df.empty if new_concept_df is not None else False}")

        if new_sector_df is not None and not new_sector_df.empty:
            logging.info(f"行业数据行数: {len(new_sector_df)}, 列名: {list(new_sector_df.columns)}")

        if new_concept_df is not None and not new_concept_df.empty:
            logging.info(f"概念数据行数: {len(new_concept_df)}, 列名: {list(new_concept_df.columns)}")

        if (new_sector_df is None or new_sector_df.empty) and (new_concept_df is None or new_concept_df.empty):
            return "无行业或概念数据"

        # 初始化标准化后的数据框列表
        standardized_dfs = []

        # 处理行业数据
        if new_sector_df is not None and not new_sector_df.empty:
            sector_df_copy = new_sector_df.copy()

            # 统一列名 - 行业数据
            # 识别名称列
            name_col = None
            for col in ['行业', '板块', '名称', '行业名称', '板块名称']:
                if col in sector_df_copy.columns:
                    name_col = col
                    break

            # 识别主力净流入列 - 扩展更多可能的列名
            flow_col = None
            for col in ['主力净流入', '净流入', '资金净流入', '今日主力净流入', '主力资金净流入', '资金流入', 'm_net', 
                       '主力净流入-净额', '主力净流入(万元)', '主力净流入万元', '主力净额']:
                if col in sector_df_copy.columns:
                    flow_col = col
                    break

            # 识别领涨股列
            leader_col = None
            for col in ['今日主力净流入最大股', '领涨股', '龙头股', '主力股', '净流入最大股', '领涨股票']:
                if col in sector_df_copy.columns:
                    leader_col = col
                    break

            # 识别涨跌幅列
            change_col = None
            for col in ['今日涨跌幅', '涨跌幅', '涨跌', '涨幅', '板块涨跌幅', '涨跌幅%']:
                if col in sector_df_copy.columns:
                    change_col = col
                    break

            # 识别主力净流入净占比列
            flow_ratio_col = None
            for col in ['今日主力净流入净占比', '主力净流入净占比', '净占比', '主力占比', '主力净流入占比', '主力净占比']:
                if col in sector_df_copy.columns:
                    flow_ratio_col = col
                    break

            # 识别超大单净流入列
            super_flow_col = None
            for col in ['今日超大单净流入', '超大单净流入', '超大单', '特大单净流入', '超大单净流入万元', '超大单净额']:
                if col in sector_df_copy.columns:
                    super_flow_col = col
                    break

            # 创建标准化的行业数据框
            sector_standardized = pd.DataFrame()
            sector_standardized['板块名称'] = sector_df_copy[name_col] if name_col else 'N/A'
            
            # 处理主力净流入字段
            if flow_col:
                sector_standardized['主力净流入'] = pd.to_numeric(sector_df_copy[flow_col], errors='coerce').fillna(0)
                # 检查数据范围，确保合理性
                flow_values = sector_standardized['主力净流入']
                logging.info(f"行业主力净流入数据范围: 最小值={flow_values.min():.2f}, 最大值={flow_values.max():.2f}, 非零数量={(flow_values != 0).sum()}")
            else:
                sector_standardized['主力净流入'] = 0
                
            sector_standardized['领涨股'] = sector_df_copy[leader_col] if leader_col else 'N/A'
            sector_standardized['今日涨跌幅'] = pd.to_numeric(sector_df_copy[change_col], errors='coerce').fillna(0) if change_col else 0
            sector_standardized['主力净流入净占比'] = pd.to_numeric(sector_df_copy[flow_ratio_col], errors='coerce').fillna(0) if flow_ratio_col else 0
            sector_standardized['超大单净流入'] = pd.to_numeric(sector_df_copy[super_flow_col], errors='coerce').fillna(0) if super_flow_col else 0
            sector_standardized['类型'] = '行业'

            # 【新增】过滤无意义的行业概念
            if not sector_standardized.empty:
                original_count = len(sector_standardized)
                sector_standardized = sector_standardized[sector_standardized['板块名称'].apply(is_meaningful_concept)]
                filtered_count = len(sector_standardized)
                logging.info(f"动态报告行业数据过滤: 原始{original_count}条 -> 过滤后{filtered_count}条")

            # 调试信息：记录列名匹配情况
            logging.info(f"行业数据列名匹配结果: 名称列={name_col}, 主力净流入列={flow_col}, 领涨股列={leader_col}, 涨跌幅列={change_col}, 净占比列={flow_ratio_col}, 超大单列={super_flow_col}")
            
            # 调试：显示标准化前后的数据对比
            if flow_col and len(sector_df_copy) > 0:
                logging.info(f"行业原始数据前3行[{flow_col}]列: {sector_df_copy[flow_col].head(3).tolist()}")
                logging.info(f"行业标准化后前3行[主力净流入]列: {sector_standardized['主力净流入'].head(3).tolist()}")
            
            standardized_dfs.append(sector_standardized)
            logging.info(f"行业数据标准化完成，添加了 {len(sector_standardized)} 条记录")

        # 处理概念数据
        if new_concept_df is not None and not new_concept_df.empty:
            concept_df_copy = new_concept_df.copy()

            # 统一列名 - 概念数据
            # 识别名称列
            name_col = None
            for col in ['概念', '板块', '名称', '概念名称', '板块名称']:
                if col in concept_df_copy.columns:
                    name_col = col
                    break

            # 识别主力净流入列 - 扩展更多可能的列名
            flow_col = None
            for col in ['主力净流入', '净流入', '资金净流入', '今日主力净流入', '主力资金净流入', '资金流入', 'm_net',
                       '主力净流入-净额', '主力净流入(万元)', '主力净流入万元', '主力净额']:
                if col in concept_df_copy.columns:
                    flow_col = col
                    break

            # 识别领涨股列
            leader_col = None
            for col in ['今日主力净流入最大股', '领涨股', '龙头股', '主力股', '净流入最大股', '领涨股票']:
                if col in concept_df_copy.columns:
                    leader_col = col
                    break

            # 识别涨跌幅列
            change_col = None
            for col in ['今日涨跌幅', '涨跌幅', '涨跌', '涨幅', '板块涨跌幅', '涨跌幅%']:
                if col in concept_df_copy.columns:
                    change_col = col
                    break

            # 识别主力净流入净占比列
            flow_ratio_col = None
            for col in ['今日主力净流入净占比', '主力净流入净占比', '净占比', '主力占比', '主力净流入占比', '主力净占比']:
                if col in concept_df_copy.columns:
                    flow_ratio_col = col
                    break

            # 识别超大单净流入列
            super_flow_col = None
            for col in ['今日超大单净流入', '超大单净流入', '超大单', '特大单净流入', '超大单净流入万元', '超大单净额']:
                if col in concept_df_copy.columns:
                    super_flow_col = col
                    break

            # 创建标准化的概念数据框
            concept_standardized = pd.DataFrame()
            concept_standardized['板块名称'] = concept_df_copy[name_col] if name_col else 'N/A'
            
            # 处理主力净流入字段
            if flow_col:
                concept_standardized['主力净流入'] = pd.to_numeric(concept_df_copy[flow_col], errors='coerce').fillna(0)
                # 检查数据范围，确保合理性
                flow_values = concept_standardized['主力净流入']
                logging.info(f"概念主力净流入数据范围: 最小值={flow_values.min():.2f}, 最大值={flow_values.max():.2f}, 非零数量={(flow_values != 0).sum()}")
            else:
                concept_standardized['主力净流入'] = 0
                
            concept_standardized['领涨股'] = concept_df_copy[leader_col] if leader_col else 'N/A'
            concept_standardized['今日涨跌幅'] = pd.to_numeric(concept_df_copy[change_col], errors='coerce').fillna(0) if change_col else 0
            concept_standardized['主力净流入净占比'] = pd.to_numeric(concept_df_copy[flow_ratio_col], errors='coerce').fillna(0) if flow_ratio_col else 0
            concept_standardized['超大单净流入'] = pd.to_numeric(concept_df_copy[super_flow_col], errors='coerce').fillna(0) if super_flow_col else 0
            concept_standardized['类型'] = '概念'

            # 【新增】过滤无意义的概念
            if not concept_standardized.empty:
                original_count = len(concept_standardized)
                concept_standardized = concept_standardized[concept_standardized['板块名称'].apply(is_meaningful_concept)]
                filtered_count = len(concept_standardized)
                logging.info(f"动态报告概念数据过滤: 原始{original_count}条 -> 过滤后{filtered_count}条")

            # 调试信息：记录列名匹配情况
            logging.info(f"概念数据列名匹配结果: 名称列={name_col}, 主力净流入列={flow_col}, 领涨股列={leader_col}, 涨跌幅列={change_col}, 净占比列={flow_ratio_col}, 超大单列={super_flow_col}")
            
            standardized_dfs.append(concept_standardized)
            logging.info(f"概念数据标准化完成，添加了 {len(concept_standardized)} 条记录")

        if not standardized_dfs:
            logging.warning("没有有效的行业或概念数据可以处理")
            return "无有效的行业或概念数据"

        # 合并与统一排名
        merged_df = pd.concat(standardized_dfs, ignore_index=True)
        logging.info(f"数据合并完成，总共 {len(merged_df)} 条记录")

        # 统计各类型数量
        type_counts = merged_df['类型'].value_counts()
        logging.info(f"数据类型分布: {dict(type_counts)}")

        # 确保主力净流入列为数值类型
        merged_df['主力净流入'] = pd.to_numeric(merged_df['主力净流入'], errors='coerce').fillna(0)
        
        # 数据质量检查
        non_zero_count = (merged_df['主力净流入'] != 0).sum()
        logging.info(f"主力净流入非零记录数: {non_zero_count}/{len(merged_df)}")
        if non_zero_count == 0:
            logging.warning("警告：所有主力净流入数据都为0，可能存在数据源字段匹配问题")

        # 基于主力净流入降序排序
        merged_df = merged_df.sort_values('主力净流入', ascending=False)

        # 只保留前100条记录
        merged_df = merged_df.head(100).reset_index(drop=True)

        # 创建新的排名列
        merged_df['排名_新'] = range(1, len(merged_df) + 1)

        # 构建超级宽表 (复用现有逻辑)
        result_df = merged_df.copy()

        # 为各个缓存数据创建排名并合并
        cache_data_list = [
            (cache_latest, '排名_上次'),
            (cache_15min, '排名_15分前'),
            (cache_30min, '排名_30分前'),
            (cache_open, '排名_开盘')
        ]

        for cache_data, rank_col_name in cache_data_list:
            if cache_data is not None:
                # 从缓存中提取行业和概念数据并标准化
                cache_standardized_dfs = []

                # 处理缓存中的行业数据
                if isinstance(cache_data, dict) and 'sector_fund_flow_df' in cache_data:
                    cache_sector_df = cache_data['sector_fund_flow_df']
                    if cache_sector_df is not None and not cache_sector_df.empty:
                        # 标准化缓存行业数据
                        cache_sector_std = pd.DataFrame()
                        # 识别名称列
                        name_col = None
                        for col in ['行业', '板块', '名称', '行业名称']:
                            if col in cache_sector_df.columns:
                                name_col = col
                                break
                        cache_sector_std['板块名称'] = cache_sector_df[name_col] if name_col else 'N/A'
                        cache_sector_std['类型'] = '行业'
                        cache_standardized_dfs.append(cache_sector_std)

                # 处理缓存中的概念数据
                if isinstance(cache_data, dict) and 'concept_fund_flow_df' in cache_data:
                    cache_concept_df = cache_data['concept_fund_flow_df']
                    if cache_concept_df is not None and not cache_concept_df.empty:
                        # 标准化缓存概念数据
                        cache_concept_std = pd.DataFrame()
                        # 识别名称列
                        name_col = None
                        for col in ['概念', '板块', '名称', '概念名称']:
                            if col in cache_concept_df.columns:
                                name_col = col
                                break
                        cache_concept_std['板块名称'] = cache_concept_df[name_col] if name_col else 'N/A'
                        cache_concept_std['类型'] = '概念'
                        cache_standardized_dfs.append(cache_concept_std)

                if cache_standardized_dfs:
                    # 合并缓存数据
                    cache_merged = pd.concat(cache_standardized_dfs, ignore_index=True)
                    cache_merged = cache_merged.head(100).reset_index(drop=True)
                    cache_merged[rank_col_name] = range(1, len(cache_merged) + 1)

                    # 合并到结果DataFrame
                    if '板块名称' in cache_merged.columns:
                        merge_df = cache_merged[['板块名称', rank_col_name]]
                        result_df = pd.merge(result_df, merge_df, on='板块名称', how='left')
                    else:
                        result_df[rank_col_name] = None
                else:
                    result_df[rank_col_name] = None
            else:
                result_df[rank_col_name] = None

        # 计算动态与高级分析列
        # 先记录哪些列有有效的历史数据
        for col in ['排名_上次', '排名_15分前', '排名_30分前', '排名_开盘']:
            if col in result_df.columns:
                # 检查该列是否有有效数据（不全是NaN）
                if not result_df[col].isna().all():
                    # 只对有部分有效数据的列使用fillna(999)
                    result_df[col] = result_df[col].fillna(999)
                else:
                    # 如果该列全是NaN，说明没有历史数据，标记为-1表示无历史数据
                    result_df[col] = -1

        # 计算排名变化
        for col, change_col in [
            ('排名_上次', '排名变化_vs_上次'),
            ('排名_15分前', '排名变化_vs_15分'),
            ('排名_30分前', '排名变化_vs_30分'),
            ('排名_开盘', '排名变化_vs_开盘')
        ]:
            if col in result_df.columns:
                result_df[change_col] = result_df.apply(
                    lambda row: (
                        row[col] - row['排名_新'] if row[col] not in [-1, 999]
                        else None
                    ), axis=1
                )

        # 计算动能状态列
        if '排名变化_vs_15分' in result_df.columns:
            def get_momentum_status(change):
                if pd.isna(change):
                    return '新上榜'
                elif change > 0:
                    return '加速!'
                elif change < 0:
                    return '减速'
                else:
                    return '匀速'

            result_df['动能状态'] = result_df['排名变化_vs_15分'].apply(get_momentum_status)
        else:
            result_df['动能状态'] = '新上榜'

        # 计算"领涨股是否核心?"列
        def check_leader_stock_core(leader_stock_name):
            if pd.isna(leader_stock_name) or leader_stock_name in ['N/A', 'None', '']:
                return '无'

            if individual_stock_df is not None and not individual_stock_df.empty:
                # 查找领涨股在个股榜中的排名
                # 识别个股数据中的名称列
                stock_name_col = None
                for col in ['名称', '股票名称', '股票简称']:
                    if col in individual_stock_df.columns:
                        stock_name_col = col
                        break

                if stock_name_col:
                    # 查找该股票的排名
                    stock_match = individual_stock_df[individual_stock_df[stock_name_col].str.contains(str(leader_stock_name), na=False, regex=False)]
                    if not stock_match.empty:
                        # 获取第一个匹配的股票排名（假设按资金流排序）
                        stock_rank = stock_match.index[0] + 1
                        if stock_rank <= 10:
                            return '是 (Top 10)'
                        elif stock_rank <= 30:
                            return '是 (Top 30)'
                        else:
                            return '否'

            return '未知'

        result_df['领涨股是否核心?'] = result_df['领涨股'].apply(check_leader_stock_core)

        # 生成报告文本
        # 核心决策摘要
        if len(result_df) > 0:
            # 市场主战场 - 排名第一的板块
            main_battlefield = result_df.iloc[0]

            # 资金断层线分析 - 使用智能断层检测算法
            gap_analysis_sector = analyze_funding_gap(
                result_df[result_df['类型'] == '行业'] if len(result_df[result_df['类型'] == '行业']) > 0 else pd.DataFrame(), 
                "行业"
            )
            gap_analysis_concept = analyze_funding_gap(
                result_df[result_df['类型'] == '概念'] if len(result_df[result_df['类型'] == '概念']) > 0 else pd.DataFrame(), 
                "概念"
            )
            
            # 保留原有断层线位置计算作为补充
            top_flow = result_df.iloc[0]['主力净流入'] if len(result_df) > 0 else 0
            断层线_threshold = top_flow * 0.3  # 30%作为断层线
            断层线_position = len(result_df[result_df['主力净流入'] >= 断层线_threshold])

            # 移除不准确的新兴热点分析

            # 移除不准确的热点轮动信号分析

            # 移除不准确的龙头共振效应分析

            # 生成行业和概念历史第一名黑马显示
            def generate_sector_concept_champions_text(champions_dict, period_key, period_name, data_type):
                champions = champions_dict[period_key]
                if not champions:
                    return f"**{period_name}{data_type}最强黑马**: 暂无历史记录，当前为首次运行或数据重置"
                
                # 按时间排序，最新的在前
                sorted_champions = sorted(champions, key=lambda x: x['time'], reverse=True)
                
                # 去重，只保留每个板块的最新记录
                unique_champions = {}
                for champion in sorted_champions:
                    name = champion['name']
                    if name not in unique_champions:
                        unique_champions[name] = champion
                
                unique_list = list(unique_champions.values())
                
                if len(unique_list) == 1:
                    # 只有一个第一名
                    champion = unique_list[0]
                    time_str = champion['time'].strftime('%H:%M')
                    return f"**{period_name}{data_type}最强黑马**: {champion['name']} - {time_str}登顶第1位，{period_name}内持续占据{data_type}资金流制高点"
                else:
                    # 多个第一名，显示最多前3名
                    top_champions = unique_list[:3]
                    
                    # 格式化显示每个冠军的登顶时间
                    champion_details = []
                    for champion in top_champions:
                        time_str = champion['time'].strftime('%H:%M')
                        champion_details.append(f"{champion['name']}({time_str})")
                    
                    total_count = len(unique_list)
                    if total_count <= 3:
                        champions_str = ', '.join(champion_details)
                        return f"**{period_name}{data_type}最强黑马**: {champions_str} - {period_name}内共{total_count}个{data_type}轮流登顶，资金流向轮动活跃"
                    else:
                        champions_str = ', '.join(champion_details)
                        return f"**{period_name}{data_type}最强黑马**: {champions_str}等{total_count}个 - {period_name}内多{data_type}轮流登顶，资金流向高度活跃"

            # 生成不同时间周期的黑马显示
            sector_15min_text = generate_sector_concept_champions_text(HISTORICAL_SECTOR_CHAMPIONS, '15min', '15分钟', '行业')
            sector_60min_text = generate_sector_concept_champions_text(HISTORICAL_SECTOR_CHAMPIONS, '60min', '60分钟', '行业')
            sector_open_text = generate_sector_concept_champions_text(HISTORICAL_SECTOR_CHAMPIONS, 'open_to_now', '开盘至今', '行业')
            concept_15min_text = generate_sector_concept_champions_text(HISTORICAL_CONCEPT_CHAMPIONS, '15min', '15分钟', '概念')
            concept_60min_text = generate_sector_concept_champions_text(HISTORICAL_CONCEPT_CHAMPIONS, '60min', '60分钟', '概念')
            concept_open_text = generate_sector_concept_champions_text(HISTORICAL_CONCEPT_CHAMPIONS, 'open_to_now', '开盘至今', '概念')

            # 格式化核心决策摘要 - 添加历史第一名黑马分析
            summary_text = f"""
## 🎯 板块资金流核心决策摘要

**市场主战场**: {main_battlefield['类型']}-{main_battlefield['板块名称']} - 资金净流入{main_battlefield['主力净流入']/10000:.1f}万，占据资金流制高点

**资金断层分析**: {gap_analysis_sector if gap_analysis_sector else gap_analysis_concept if gap_analysis_concept else f'前{断层线_position}名形成第一梯队，建议关注头部板块资金流向'}

{sector_15min_text}

{sector_60min_text}

{sector_open_text}

{concept_15min_text}

{concept_60min_text}

{concept_open_text}
"""
        else:
            summary_text = "## 🎯 板块资金流核心决策摘要\n\n**数据状态**: 暂无有效的行业概念数据"

        # 分别生成行业和概念动态变化看板
        if len(result_df) > 0:
            # 格式化排名变化列
            def format_change_for_display(value):
                if pd.isna(value):
                    return "新上榜"
                elif value == 0:
                    return "→0"
                elif value > 0:
                    return f"↑{int(value)}"
                else:
                    return f"↓{int(abs(value))}"

            # 格式化数值显示
            def format_money(value):
                if pd.isna(value) or value == 0:
                    return "0万"
                
                # 智能判断数值单位
                abs_value = abs(value)
                if abs_value >= 100000000:  # 亿级别
                    return f"{value/100000000:.2f}亿"
                elif abs_value >= 10000:  # 万级别，数据源可能已经是万为单位
                    return f"{value:.1f}万"
                else:  # 元为单位，需要转换为万
                    return f"{value/10000:.1f}万"

            def format_percentage(value):
                if pd.isna(value) or value == 0:
                    return "0.00%"
                return f"{value:.2f}%"

            # 分离行业和概念数据
            sector_df = result_df[result_df['类型'] == '行业'].head(15).copy()
            concept_df = result_df[result_df['类型'] == '概念'].head(15).copy()
            
            # 为行业和概念数据分别进行断层分析
            sector_gap_section = ""
            concept_gap_section = ""
            
            if len(sector_df) > 0:
                sector_gap_analysis = analyze_funding_gap(sector_df, "行业")
                if sector_gap_analysis:
                    sector_gap_section = f"\n### 💎 行业资金流断层分析\n{sector_gap_analysis}\n"
            
            if len(concept_df) > 0:
                concept_gap_analysis = analyze_funding_gap(concept_df, "概念")
                if concept_gap_analysis:
                    concept_gap_section = f"\n### 💎 概念资金流断层分析\n{concept_gap_analysis}\n"

            # 生成行业看板
            sector_board = ""
            if len(sector_df) > 0:
                # 添加格式化列
                sector_df['排名变化(vs15m)'] = sector_df['排名变化_vs_15分'].apply(format_change_for_display)
                sector_df['主力净流入(万)'] = sector_df['主力净流入'].apply(format_money)
                sector_df['涨跌幅'] = sector_df['今日涨跌幅'].apply(format_percentage)
                sector_df['主力净占比'] = sector_df['主力净流入净占比'].apply(format_percentage)
                sector_df['超大单(万)'] = sector_df['超大单净流入'].apply(format_money)

                # 选择显示列
                sector_display = sector_df[['板块名称', '排名_新', '排名变化(vs15m)', '动能状态', '主力净流入(万)', '涨跌幅', '主力净占比', '超大单(万)', '领涨股', '领涨股是否核心?']].copy()
                sector_display.columns = ['行业名称', '当前排名', '排名变化(vs15m)', '动能状态', '主力净流入', '今日涨跌幅', '主力净占比', '超大单净流入', '领涨股', '领涨股是否核心?']

                sector_table = sector_display.to_markdown(index=False)
                sector_board = f"""
## 📊 行业动态变化看板 (前15名)

{sector_table}
"""

            # 生成概念看板
            concept_board = ""
            if len(concept_df) > 0:
                # 添加格式化列
                concept_df['排名变化(vs15m)'] = concept_df['排名变化_vs_15分'].apply(format_change_for_display)
                concept_df['主力净流入(万)'] = concept_df['主力净流入'].apply(format_money)
                concept_df['涨跌幅'] = concept_df['今日涨跌幅'].apply(format_percentage)
                concept_df['主力净占比'] = concept_df['主力净流入净占比'].apply(format_percentage)
                concept_df['超大单(万)'] = concept_df['超大单净流入'].apply(format_money)

                # 选择显示列
                concept_display = concept_df[['板块名称', '排名_新', '排名变化(vs15m)', '动能状态', '主力净流入(万)', '涨跌幅', '主力净占比', '超大单(万)', '领涨股', '领涨股是否核心?']].copy()
                concept_display.columns = ['概念名称', '当前排名', '排名变化(vs15m)', '动能状态', '主力净流入', '今日涨跌幅', '主力净占比', '超大单净流入', '领涨股', '领涨股是否核心?']

                concept_table = concept_display.to_markdown(index=False)
                concept_board = f"""
## 📊 概念动态变化看板 (前15名)

{concept_table}
"""

            # 组合最终报告
            final_report = f"""{summary_text}
{sector_gap_section}
{sector_board}
{concept_gap_section}
{concept_board}

---
*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*数据说明: ↑表示排名上升，↓表示排名下降，→表示无变化，"新上榜"表示首次出现或无历史对比数据*
*动能状态: 加速!表示资金加速流入，减速表示资金流入放缓，匀速表示资金流入稳定*
*资金单位: 万元，占比为百分比*
"""
        else:
            final_report = summary_text

        return final_report

    except Exception as e:
        logging.error(f"生成行业概念动态报告时发生错误: {e}")
        return f"行业概念动态报告生成失败: {str(e)}"

# --- 【新功能】从 get_all_capital_flow_east.py 移植的核心文件写入函数 ---
def write_signal_file_atomically(file_path, data, overwrite=False):
    """
    (线程安全且进程安全) 使用"写入临时文件再替换"的原子操作模式创建或更新信号文件。
    这可以彻底避免iQuant端读取到0字节或不完整文件的问题。

    :param file_path: 目标文件路径, e.g., "D:/gemini_buy.ebk" 或 "D:/gemini_signal.json"
    :param data: 要写入的数据，可以是股票代码列表(兼容旧版)或Python字典/列表(新版JSON)
    :param overwrite: 是否覆盖文件。True=覆盖(用于买入)，False=合并(用于卖出)
    """
    import json

    # 判断文件类型
    is_json_file = file_path.lower().endswith('.json')

    if not data:
        logging.debug(f"无新信号数据需要写入 {file_path}，跳过文件操作。")
        # 如果是覆盖模式且无信号，则删除旧文件，确保iQuant不会读到过期信号
        if overwrite and os.path.exists(file_path):
            try:
                with file_lock:
                    os.remove(file_path)
                    logging.info(f"无AI推荐信号，已删除旧的信号文件: {file_path}")
                    print(f"无AI推荐信号，已删除旧的信号文件: {file_path}")
            except Exception as e:
                logging.error(f"删除旧信号文件 {file_path} 失败: {e}")
        return

    with file_lock:
        try:
            if is_json_file:
                # JSON文件处理逻辑
                if isinstance(data, (dict, list)):
                    # 创建临时文件
                    temp_file_path = file_path + ".tmp"
                    with open(temp_file_path, 'w', encoding='utf-8') as f:
                        json.dump(data, f, ensure_ascii=False, indent=4)

                    # 使用os.replace()进行原子性替换
                    os.replace(temp_file_path, file_path)

                    signal_count = len(data.get('signals', [])) if isinstance(data, dict) else len(data)
                    logging.info(f"成功生成JSON信号文件: {file_path}，包含 {signal_count} 条信号。")
                    print(f"成功生成JSON信号文件: {file_path}，包含 {signal_count} 条信号。")
                else:
                    logging.error(f"JSON文件要求数据为字典或列表格式，实际收到: {type(data)}")
                    return
            else:
                # EBK文件处理逻辑（兼容旧版）
                new_codes = data if isinstance(data, list) else []

                final_codes = set(new_codes)
                # 合并模式：如果不是覆盖，则读取旧文件中的代码并合并
                if not overwrite and os.path.exists(file_path):
                    try:
                        # 使用GBK编码读取，以防万一旧文件是GBK格式
                        with open(file_path, 'r', encoding='gbk', errors='ignore') as f:
                            existing_codes = {line.strip() for line in f if line.strip()}
                            final_codes.update(existing_codes)
                    except Exception as e:
                        logging.warning(f"读取旧信号文件 {file_path} 以进行合并时失败: {e}。将只写入新信号。")

                sorted_codes = sorted(list(final_codes))

                # 创建一个与目标文件在同一目录的临时文件
                temp_dir = os.path.dirname(file_path)
                # 使用 tempfile 在同一目录下创建临时文件
                with tempfile.NamedTemporaryFile('w', dir=temp_dir, delete=False, encoding='gbk', suffix='.tmp') as tf:
                    temp_file_path = tf.name
                    for code in sorted_codes:
                        # 保持原有的换行+代码格式
                        tf.write(f"\n{code}")

                # 使用os.replace()进行原子性替换
                os.replace(temp_file_path, file_path)

                action = "覆盖生成" if overwrite else "更新合并"
                logging.info(f"成功{action}AI信号文件: {file_path}，包含 {len(sorted_codes)} 条代码。")
                print(f"成功{action}AI信号文件: {file_path}，包含 {len(sorted_codes)} 条代码。")

        except Exception as e:
            logging.error(f"原子写入AI信号文件 {file_path} 失败: {e}")
            print(f"原子写入AI信号文件 {file_path} 失败: {e}")
            # 如果替换失败，尝试清理临时文件
            if 'temp_file_path' in locals() and os.path.exists(temp_file_path):
                os.remove(temp_file_path)


# --- 【新功能】用于解析和保存AI推荐股票的函数 ---
def parse_and_save_gemini_watchlist(response_text, file_path):
    """
    解析Gemini响应文本中的“最终核心观察池”，提取股票代码并保存到信号文件。

    :param response_text: Gemini API返回的原始文本。
    :param file_path: 信号文件的保存路径 (e.g., "D:/gemini_buy.ebk")
    """
    logging.info(f"开始解析AI响应，准备生成信号文件: {file_path}")
    stock_codes = []
    try:
        # 1. 定位“最终核心观察池”或“明日核心交易剧本”部分，以提高解析的准确性
        watchlist_section_start = response_text.find("最终核心观察池")
        if watchlist_section_start == -1:
            watchlist_section_start = response_text.find("明日核心交易剧本")

        if watchlist_section_start == -1:
            logging.warning("在AI响应中未找到'最终核心观察池'或'明日核心交易剧本'部分，无法提取股票代码。")
            # 如果找不到观察池，就写入一个空列表来清空旧文件
            write_signal_file_atomically(file_path, [], overwrite=True)
            return

        watchlist_text = response_text[watchlist_section_start:]

        # 2. 使用正则表达式查找所有6位数字的股票代码
        # \b(\d{6})\b 匹配独立的6位数字，避免匹配到其他数字的一部分
        found_codes = re.findall(r'\b(\d{6})\b', watchlist_text)

        if found_codes:
            # 去重并保持顺序
            for code in found_codes:
                if code not in stock_codes:
                    stock_codes.append(code)
            logging.info(f"从AI响应中成功提取到 {len(stock_codes)} 个不重复的股票代码: {stock_codes}")
        else:
            logging.warning("在指定区域未找到任何股票代码。")

        # 3. 调用文件写入函数，覆盖保存
        # 这里的 overwrite=True 表示每次都用最新的AI推荐覆盖旧文件
        write_signal_file_atomically(file_path, stock_codes, overwrite=True)

    except Exception as e:
        logging.error(f"解析和保存AI推荐股票时发生错误: {e}")


def parse_and_save_gemini_watchlist_new(response_text, file_path):
    """
    新版本：解析Gemini响应文本中的"最终交易计划汇总"或"模拟买入计划"表格，
    提取股票信号并保存到JSON格式的信号文件。

    :param response_text: Gemini API返回的原始文本。
    :param file_path: 信号文件的保存路径 (e.g., "D:/gemini_signal.json")
    """
    logging.info(f"开始解析AI响应，准备生成信号文件: {file_path}")

    try:
        # 解析AI响应中的表格数据
        signals = parse_trading_plan_table(response_text)

        if not signals:
            logging.warning("未能从AI响应中解析出有效的交易信号")
            # 如果没有信号，写入空的JSON结构
            empty_data = {
                "version": "1.0",
                "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                "signals": []
            }
            write_signal_file_atomically(file_path, empty_data, overwrite=True)
            return

        # 构建JSON数据结构
        signal_data = {
            "version": "1.0",
            "timestamp": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "signals": signals
        }

        # 写入JSON文件
        write_signal_file_atomically(file_path, signal_data, overwrite=True)

        logging.info(f"成功解析并保存 {len(signals)} 条交易信号到: {file_path}")

        # 兼容性：同时生成传统的EBK文件
        ebk_file_path = file_path.replace('.json', '.ebk')
        stock_codes = [signal['code'] for signal in signals if signal.get('type') == 'buy']
        if stock_codes:
            write_signal_file_atomically(ebk_file_path, stock_codes, overwrite=True)
            logging.info(f"兼容性：同时生成EBK文件 {ebk_file_path}，包含 {len(stock_codes)} 个买入代码")

    except Exception as e:
        logging.error(f"解析和保存AI推荐股票时发生错误: {e}")


def parse_trading_plan_table(response_text):
    """
    解析AI响应中的"最终交易计划汇总"或"模拟买入计划"表格，
    提取股票代码、名称、金额、理由等信息。

    :param response_text: AI响应的原始文本
    :return: 信号列表，每个信号是一个字典
    """
    signals = []

    try:
        # 1. 寻找表格部分
        table_patterns = [
            "最终交易计划汇总",
            "模拟买入计划",
            "模拟卖出计划",
            "交易计划汇总"
        ]

        table_start = -1
        for pattern in table_patterns:
            table_start = response_text.find(pattern)
            if table_start != -1:
                logging.info(f"找到表格部分: {pattern}")
                break

        if table_start == -1:
            logging.warning("未找到交易计划表格部分")
            return signals

        # 提取表格部分的文本
        table_text = response_text[table_start:]

        # 2. 使用正则表达式解析表格行
        # 匹配表格行格式：归属单元\t股票代码\t股票名称\t买入金额\t理由
        table_pattern = r'([A-Z]{1,3})\s*[|\t]\s*(\d{6})\s*[|\t]\s*([^\t|]+?)\s*[|\t]\s*([\d,]+)\s*[|\t]\s*([^\n\r|]+)'

        matches = re.findall(table_pattern, table_text, re.MULTILINE)

        if not matches:
            # 尝试更宽松的匹配模式
            logging.info("尝试使用更宽松的表格解析模式")
            # 匹配包含6位数字代码的行
            line_pattern = r'.*?(\d{6}).*?([^\d\s][^\t|]*?)\s*[|\t]\s*([\d,]+).*?([^\n\r]{10,})'
            matches = re.findall(line_pattern, table_text, re.MULTILINE)

            for match in matches:
                code, name, amount_str, reason = match
                # 推断归属单元
                level = infer_signal_level(reason, amount_str)

                signal = {
                    "code": code.strip(),
                    "type": "buy",  # 默认为买入
                    "level": level,
                    "reason": reason.strip().rstrip('|').strip()[:100],  # 清理末尾的|字符并限制理由长度
                    "plan_amount": parse_amount(amount_str)
                }
                signals.append(signal)
                logging.info(f"解析信号: {signal}")
        else:
            # 标准表格格式解析
            for match in matches:
                unit, code, name, amount_str, reason = match

                signal = {
                    "code": code.strip(),
                    "type": "buy",  # 默认为买入，可根据需要扩展
                    "level": unit.strip(),
                    "reason": reason.strip().rstrip('|').strip()[:100],  # 清理末尾的|字符并限制理由长度
                    "plan_amount": parse_amount(amount_str)
                }
                signals.append(signal)
                logging.info(f"解析信号: {signal}")

        # 3. 如果表格解析失败，尝试从文本中提取股票代码和相关信息
        if not signals:
            logging.info("表格解析失败，尝试从文本中提取股票信息")
            signals = extract_stocks_from_text(response_text)

        return signals

    except Exception as e:
        logging.error(f"解析交易计划表格时发生错误: {e}")
        return signals


def parse_amount(amount_str):
    """
    解析金额字符串，返回数值

    :param amount_str: 金额字符串，如 "150,000" 或 "83300"
    :return: 数值
    """
    try:
        # 移除逗号和其他非数字字符
        clean_str = re.sub(r'[^\d.]', '', amount_str)
        return int(float(clean_str)) if clean_str else 0
    except:
        return 0


def infer_signal_level(reason, amount_str):
    """
    根据理由和金额推断信号等级

    :param reason: 入选理由
    :param amount_str: 金额字符串
    :return: 信号等级
    """
    reason_lower = reason.lower()
    amount = parse_amount(amount_str)

    # 根据关键词判断
    if any(keyword in reason_lower for keyword in ['攻坚', '冠军', '龙头', '核心']):
        return 'AF'
    elif any(keyword in reason_lower for keyword in ['主战', '中军', '主线']):
        return 'MBG'
    elif any(keyword in reason_lower for keyword in ['潜伏', '试错', '彩票']):
        return 'SO'
    elif amount >= 100000:
        return 'AF'
    elif amount >= 50000:
        return 'MBG'
    else:
        return 'SO'


def extract_stocks_from_text(response_text):
    """
    从文本中提取股票代码和相关信息的备用方法

    :param response_text: AI响应文本
    :return: 信号列表
    """
    signals = []

    try:
        # 查找所有6位数字的股票代码
        code_pattern = r'\b(\d{6})\b'
        codes = re.findall(code_pattern, response_text)

        # 为每个代码创建基本信号
        for code in codes[:10]:  # 限制最多10个
            signal = {
                "code": code,
                "type": "buy",
                "level": "MBG",
                "reason": "AI推荐股票",
                "plan_amount": 50000
            }
            signals.append(signal)

        logging.info(f"从文本中提取到 {len(signals)} 个股票代码")

    except Exception as e:
        logging.error(f"从文本提取股票信息时发生错误: {e}")

    return signals


def is_trading_time():
    """
    判断当前时间是否在A股交易时间（9:25:00-11:30:00，13:00:00-15:00:00）。
    """
    if not ENABLE_TRADING_TIME_CHECK:
        return True

    now = datetime.now().time()
    morning_start = time(9, 25)
    morning_end = time(11, 30)
    afternoon_start = time(13, 0)
    afternoon_end = time(15, 0)
    return (morning_start <= now < morning_end) or (afternoon_start <= now < afternoon_end)


def fetch_and_save_previous_zt_pool_data():
    """
    获取东方财富“昨日”涨停股池数据并保存。
    """
    try:
        logging.info("正在获取昨日涨停股池数据...")
        now = datetime.now()
        date_str = now.strftime('%Y%m%d')

        # 获取交易日历以确定上一个交易日
        trade_date_df = ak.tool_trade_date_hist_sina()
        trade_dates = pd.to_datetime(trade_date_df['trade_date']).dt.strftime('%Y%m%d')

        # 找到当前日期的上一个交易日
        previous_trade_dates = trade_dates[trade_dates < date_str]
        if previous_trade_dates.empty:
            logging.warning("无历史交易日数据，无法获取昨日涨停股池。")
            return None, None

        last_trade_date = previous_trade_dates.iloc[-1]
        logging.info(f"定位到上一个交易日为: {last_trade_date}")

        df = ak.stock_zt_pool_previous_em(date=last_trade_date)

        if df.empty:
            logging.warning(f"日期 {last_trade_date} 未获取到昨日涨停股池数据。")
            return None, None

        logging.info(f"成功获取 {len(df)} 条昨日涨停股池数据。")

        # 过滤ST和*ST股票
        df = df[~df['名称'].str.contains('ST|\\*ST', na=False)]

        # 保存到本地
        date_folder = now.strftime('%Y-%m-%d')
        filename = f"{now.strftime('%H-%M')}_previous_zt_pool.csv"

        data_dir = os.path.join("fund_data", date_folder)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        filepath = os.path.join(data_dir, filename)
        df.to_csv(filepath, index=False, encoding='utf_8_sig')
        logging.info(f"昨日涨停股池数据已保存到: {filepath}")
        return df, filepath

    except Exception as e:
        logging.error(f"获取昨日涨停股池数据时发生错误: {e}")
        return None, None


def fetch_and_save_news_data():
    """
    【已修改】获取多家财经新闻快讯并整合成一个文本返回。
    新增：财新网
    """
    logging.info("正在获取多家财经新闻快讯...")
    all_news_content = []

    try:
        # 东方财富
        df_em = ak.stock_info_global_em().head(30)
        # 【修改】手动格式化字符串以消除不必要的空格
        em_news_lines = []
        if '摘要' in df_em.columns:
            for _, row in df_em.iterrows():
                # 清理摘要列的前后空格
                summary_text = str(row['摘要']).strip()
                em_news_lines.append(f"{row['发布时间']} {summary_text}")
        news_em = "\n--- 东方财富快讯 ---\n" + "\n".join(em_news_lines)
        all_news_content.append(news_em)
        logging.info(f"成功获取东方财富快讯 {len(df_em)} 条。")
    except Exception as e:
        logging.error(f"获取东方财富快讯失败: {e}")
        all_news_content.append("\n--- 东方财富快讯 ---\n获取失败")

    try:
        # 同花顺
        df_ths = ak.stock_info_global_ths().head(30)
        # 【修改】手动格式化字符串以消除不必要的空格
        ths_news_lines = [f"{row['发布时间']} {str(row['标题']).strip()}" for _, row in df_ths.iterrows()]
        news_ths = "\n--- 同花顺财经直播 ---\n" + "\n".join(ths_news_lines)
        all_news_content.append(news_ths)
        logging.info(f"成功获取同花顺财经直播 {len(df_ths)} 条。")
    except Exception as e:
        logging.error(f"获取同花顺财经直播失败: {e}")
        all_news_content.append("\n--- 同花顺财经直播 ---\n获取失败")

    try:
        # 财联社
        df_cls = ak.stock_info_global_cls(symbol="全部").head(30)
        # 【修改】手动格式化字符串以消除不必要的空格
        cls_news_lines = []
        if '内容' in df_cls.columns:
            for _, row in df_cls.iterrows():
                # 清理内容列的前后空格
                content_text = str(row['内容']).strip()
                cls_news_lines.append(f"{row['发布时间']} {content_text}")
        news_cls = "\n--- 财联社电报 ---\n" + "\n".join(cls_news_lines)
        all_news_content.append(news_cls)
        logging.info(f"成功获取财联社电报 {len(df_cls)} 条。")
    except Exception as e:
        logging.error(f"获取财联社电报失败: {e}")
        all_news_content.append("\n--- 财联社电报 ---\n获取失败")

    try:
        # 【新增】财新网-内容精选
        df_cx = ak.stock_news_main_cx()
        # 【修改】只保留最近3天的数据
        df_cx['pub_time'] = pd.to_datetime(df_cx['pub_time'], errors='coerce')
        df_cx.dropna(subset=['pub_time'], inplace=True)
        three_days_ago = pd.Timestamp.now().normalize() - pd.Timedelta(days=2)
        df_cx = df_cx[df_cx['pub_time'] >= three_days_ago].copy().head(30)

        # 【修改】手动格式化字符串以消除不必要的空格
        cx_news_lines = [f"{row['pub_time']} {str(row['summary']).strip()}" for _, row in df_cx.iterrows()]
        news_cx = "\n--- 财新网内容精选 ---\n" + "\n".join(cx_news_lines)
        all_news_content.append(news_cx)
        logging.info(f"成功获取财新网内容精选（最近3日） {len(df_cx)} 条。")
    except Exception as e:
        logging.error(f"获取财新网内容精选失败: {e}")
        all_news_content.append("\n--- 财新网内容精选 ---\n获取失败")

    return "\n".join(all_news_content)


def fetch_and_save_technical_indicators_data():
    """
    获取同花顺五大技术指标数据，并以字典形式返回DataFrame。
    """
    logging.info("正在获取系列技术指标数据...")
    indicators_data = {}

    try:
        df_cxg = ak.stock_rank_cxg_ths(symbol="创月新高")
        indicators_data['创月新高'] = df_cxg[~df_cxg['股票简称'].str.contains('ST|\\*ST', na=False)]
        logging.info(f"成功获取'创月新高'数据 {len(df_cxg)} 条。")
    except Exception as e:
        logging.error(f"获取'创月新高'数据失败: {e}")

    try:
        df_lxsz = ak.stock_rank_lxsz_ths()
        indicators_data['连续上涨'] = df_lxsz[~df_lxsz['股票简称'].str.contains('ST|\\*ST', na=False)]
        logging.info(f"成功获取'连续上涨'数据 {len(df_lxsz)} 条。")
    except Exception as e:
        logging.error(f"获取'连续上涨'数据失败: {e}")

    try:
        df_cxfl = ak.stock_rank_cxfl_ths()
        indicators_data['持续放量'] = df_cxfl[~df_cxfl['股票简称'].str.contains('ST|\\*ST', na=False)]
        logging.info(f"成功获取'持续放量'数据 {len(df_cxfl)} 条。")
    except Exception as e:
        logging.error(f"获取'持续放量'数据失败: {e}")

    try:
        df_xstp = ak.stock_rank_xstp_ths(symbol="20日均线")  # 使用20日线作为中期突破参考
        indicators_data['向上突破'] = df_xstp[~df_xstp['股票简称'].str.contains('ST|\\*ST', na=False)]
        logging.info(f"成功获取'向上突破20日均线'数据 {len(df_xstp)} 条。")
    except Exception as e:
        logging.error(f"获取'向上突破'数据失败: {e}")

    try:
        df_ljqs = ak.stock_rank_ljqs_ths()
        indicators_data['量价齐升'] = df_ljqs[~df_ljqs['股票简称'].str.contains('ST|\\*ST', na=False)]
        logging.info(f"成功获取'量价齐升'数据 {len(df_ljqs)} 条。")
    except Exception as e:
        logging.error(f"获取'量价齐升'数据失败: {e}")

    # 将获取到的数据保存到本地文件
    now = datetime.now()
    date_folder = now.strftime('%Y-%m-%d')
    data_dir = os.path.join("fund_data", date_folder)
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)

    for name, df in indicators_data.items():
        if df is not None and not df.empty:
            filename = f"{now.strftime('%H-%M')}_indicator_{name}.csv"
            filepath = os.path.join(data_dir, filename)
            df.to_csv(filepath, index=False, encoding='utf_8_sig')
            logging.info(f"技术指标 '{name}' 数据已保存到: {filepath}")

    return indicators_data


def fetch_and_save_dzjy_mrmx_data():
    """
    获取东方财富大宗交易-每日明细数据并保存。
    """
    try:
        logging.info("正在获取大宗交易-每日明细数据...")
        now = datetime.now()
        date_str_today = now.strftime('%Y%m%d')

        # --- 修复逻辑：获取最近的交易日 ---
        trade_date_df = ak.tool_trade_date_hist_sina()
        trade_dates = pd.to_datetime(trade_date_df['trade_date']).dt.strftime('%Y%m%d')
        # 找到不晚于今天的最近一个交易日
        last_trade_date = trade_dates[trade_dates <= date_str_today].iloc[-1]
        logging.info(f"定位到最近交易日为: {last_trade_date} 以获取大宗交易数据。")

        df = ak.stock_dzjy_mrmx(symbol='A股', start_date=last_trade_date, end_date=last_trade_date)

        if df.empty:
            logging.warning(f"日期 {last_trade_date} 未获取到大宗交易每日明细数据。")
            return None, None

        logging.info(f"成功获取 {len(df)} 条大宗交易每日明细数据。")

        # 保存到本地
        date_folder = now.strftime('%Y-%m-%d')
        filename = f"{now.strftime('%H-%M')}_dzjy_mrmx.csv"
        data_dir = os.path.join("fund_data", date_folder)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        filepath = os.path.join(data_dir, filename)
        df.to_csv(filepath, index=False, encoding='utf_8_sig')
        logging.info(f"大宗交易每日明细数据已保存到: {filepath}")
        return df, filepath

    except Exception as e:
        logging.error(f"获取大宗交易每日明细数据时发生错误: {e}")
        return None, None


def fetch_and_save_market_movers_data():
    """
    【新增】获取东方财富盘口异动数据，并整合成一个文本返回。
    【修改】增加20秒超时机制，超时则跳过该异动类型。
    """
    logging.info("正在获取盘口异动数据...")
    all_movers_content = []

    # 定义需要关注的异动类型
    mover_types = {
        '火箭发射': '正面-拉升',
        '大笔买入': '正面-资金',
    }

    now = datetime.now()
    date_folder = now.strftime('%Y-%m-%d')
    data_dir = os.path.join("fund_data", date_folder)
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)

    for symbol, desc in mover_types.items():
        try:
            logging.info(f"开始获取盘口异动 '{symbol}' 数据...")
            start_time = time_module.time()

            # 设置20秒超时
            import signal
            
            def timeout_handler(signum, frame):
                raise TimeoutError(f"获取 '{symbol}' 数据超时")
            
            # 在Windows上使用threading.Timer替代signal (signal在Windows上不可靠)
            import threading
            timeout_occurred = [False]
            
            def timeout_func():
                timeout_occurred[0] = True
            
            timer = threading.Timer(20.0, timeout_func)
            timer.start()
            
            try:
                df = ak.stock_changes_em(symbol=symbol)
                
                # 检查是否超时
                if timeout_occurred[0]:
                    raise TimeoutError(f"获取 '{symbol}' 数据超时(>20秒)")
                    
            finally:
                timer.cancel()

            elapsed_time = time_module.time() - start_time

            if not df.empty:
                # 过滤ST
                original_count = len(df)
                df = df[~df['名称'].str.contains('ST|\\*ST', na=False)]
                filtered_count = len(df)

                logging.info(f"'{symbol}' 数据获取完成 - 耗时: {elapsed_time:.2f}秒, 原始数据: {original_count}条, 过滤后: {filtered_count}条")

                # 保存文件
                filename = f"{now.strftime('%H-%M')}_movers_{symbol}.csv"
                filepath = os.path.join(data_dir, filename)
                df.to_csv(filepath, index=False, encoding='utf_8_sig')
                logging.info(f"盘口异动 '{symbol}' 数据已保存到: {filepath}")

                # 【新增】对大笔买入和火箭发射数据进行深度分析，其他异动类型保持原有逻辑
                if symbol == '大笔买入':
                    analyzed_text = analyze_big_buy_data(df, symbol)
                    mover_text = f"\n--- 盘口异动: {symbol} ({desc}) ---\n" + analyzed_text
                elif symbol == '火箭发射':
                    analyzed_text = analyze_rocket_launch_data(df, symbol)
                    mover_text = f"\n--- 盘口异动: {symbol} ({desc}) ---\n" + analyzed_text
                else:
                    # 其他异动类型保持原有逻辑，取最新30条
                    mover_text = f"\n--- 盘口异动: {symbol} ({desc}) ---\n" + df.head(30).to_string(index=False)
                
                all_movers_content.append(mover_text)
            else:
                logging.warning(f"'{symbol}' 数据获取完成但为空 - 耗时: {elapsed_time:.2f}秒")
        except TimeoutError as te:
            elapsed_time = time_module.time() - start_time if 'start_time' in locals() else 0
            logging.warning(f"获取盘口异动 '{symbol}' 数据超时(>20秒) - 耗时: {elapsed_time:.2f}秒, 跳过")
            all_movers_content.append(f"\n--- 盘口异动: {symbol} ({desc}) ---\n获取超时，已跳过")
        except Exception as e:
            elapsed_time = time_module.time() - start_time if 'start_time' in locals() else 0
            logging.error(f"获取盘口异动 '{symbol}' 数据失败 - 耗时: {elapsed_time:.2f}秒, 错误: {e}")
            all_movers_content.append(f"\n--- 盘口异动: {symbol} ({desc}) ---\n获取失败: {str(e)}")

    if not all_movers_content:
        return "无盘口异动数据"

    return "\n".join(all_movers_content)


def analyze_big_buy_data(df, symbol="大笔买入"):
    """
    深度分析大笔买入数据，解析相关信息并进行聚合计算
    
    :param df: 大笔买入数据DataFrame
    :param symbol: 异动类型名称
    :return: 分析后的数据字符串
    """
    try:
        if df is None or df.empty:
            return f"无{symbol}数据"
        
        logging.info(f"开始分析{symbol}数据，原始数据共{len(df)}条")
        
        # 数据预处理和清洗
        analysis_df = df.copy()
        
        # 第一步：数据解析与规整
        if '相关信息' in analysis_df.columns:
            # 解析相关信息列
            parsed_data = []
            
            for idx, row in analysis_df.iterrows():
                try:
                    info = str(row['相关信息']).strip()
                    if info and info != 'nan':
                        # 分割相关信息字符串
                        parts = info.split(',')
                        
                        # 提取各个字段
                        volume = float(parts[0]) if len(parts) > 0 and parts[0].strip() else 0  # 成交量(股)
                        price = float(parts[1]) if len(parts) > 1 and parts[1].strip() else 0   # 成交价格
                        indicator = float(parts[2]) if len(parts) > 2 and parts[2].strip() else 0  # 异动指标
                        amount = float(parts[3]) if len(parts) > 3 and parts[3].strip() else 0   # 成交额(元)
                        
                        # 转换成交额为万元
                        amount_wan = amount / 10000 if amount > 0 else 0
                        
                        parsed_data.append({
                            '时间': row.get('时间', ''),
                            '代码': row.get('代码', ''),
                            '名称': row.get('名称', ''),
                            '板块': symbol,
                            '异动成交量(股)': volume,
                            '异动成交价': price,
                            '异动强度指标': indicator,
                            '异动成交额(万元)': amount_wan
                        })
                        
                except (ValueError, IndexError) as e:
                    # 解析失败的记录跳过
                    logging.warning(f"解析相关信息失败: {info}, 错误: {e}")
                    continue
            
            if not parsed_data:
                return f"无法解析{symbol}数据的相关信息"
            
            # 创建解析后的DataFrame
            parsed_df = pd.DataFrame(parsed_data)
            
            # 第二步：数据聚合计算
            if '代码' in parsed_df.columns and not parsed_df.empty:
                # 按股票代码聚合
                aggregated = parsed_df.groupby(['代码', '名称']).agg({
                    '异动成交量(股)': 'sum',           # 累计成交量
                    '异动成交额(万元)': 'sum',          # 累计成交额
                    '异动成交价': 'mean',              # 平均成交价
                    '异动强度指标': 'mean',            # 平均异动强度
                    '时间': ['min', 'max', 'count']    # 首次时间、末次时间、异动次数
                }).round(2)
                
                # 重新整理列名
                aggregated.columns = ['累计成交量(股)', '累计成交额(万元)', '平均成交价', '平均异动强度', '首次异动时间', '末次异动时间', '异动次数']
                aggregated = aggregated.reset_index()
                
                # 按累计成交额排序
                aggregated = aggregated.sort_values('累计成交额(万元)', ascending=False)
                
                # 计算总体统计
                total_amount = parsed_df['异动成交额(万元)'].sum()
                total_volume = parsed_df['异动成交量(股)'].sum()
                unique_stocks = len(aggregated)
                total_transactions = len(parsed_df)
                
                # 生成分析报告
                analysis_summary = f"""
=== {symbol}数据深度分析 ===
📊 数据概况：
- 原始异动记录：{total_transactions}条
- 涉及股票数量：{unique_stocks}只
- 累计成交额：{total_amount:,.2f}万元
- 累计成交量：{total_volume:,.0f}股

🏆 TOP 10 {symbol}标的：
"""
                
                # 添加TOP 10详细信息
                for i, (idx, row) in enumerate(aggregated.head(10).iterrows(), 1):
                    analysis_summary += f"{i:2d}. {row['名称']}({row['代码']}): "
                    analysis_summary += f"累计{row['累计成交额(万元)']:,.2f}万元, "
                    analysis_summary += f"异动{row['异动次数']}次, "
                    analysis_summary += f"均价{row['平均成交价']:.2f}元\n"
                
                # 添加市场洞察
                if len(aggregated) > 0:
                    top5_concentration = aggregated.head(5)['累计成交额(万元)'].sum() / total_amount
                    analysis_summary += f"""
💡 市场洞察：
- 平均单笔成交额：{total_amount/total_transactions:.2f}万元
- TOP5资金集中度：{top5_concentration:.1%}
- 市场活跃度：{'高' if total_transactions > 100 else '中等' if total_transactions > 50 else '一般'}

"""
                
                # 拼接完整数据 (显示前50名)
                display_data = aggregated.head(50)
                result = analysis_summary + f"\n=== 前50名聚合数据 ===\n" + display_data.to_string(index=False)
                
                logging.info(f"{symbol}数据分析完成，聚合后{len(aggregated)}只股票，显示前{len(display_data)}名")
                return result
            
            else:
                # 如果聚合失败，返回解析后的数据
                display_data = parsed_df.head(50)
                return f"=== {symbol}解析数据（前50条） ===\n" + display_data.to_string(index=False)
                
        else:
            # 如果没有相关信息列，返回原始数据
            logging.warning(f"{symbol}数据中未找到'相关信息'列，返回原始数据")
            return f"=== {symbol}原始数据（前30条） ===\n" + df.head(30).to_string(index=False)
            
    except Exception as e:
        logging.error(f"分析{symbol}数据时发生错误: {e}")
        # 出错时返回简单版本
        if df is not None and not df.empty:
            return f"=== {symbol}数据（简化版前30条） ===\n" + df.head(30).to_string(index=False)
        return f"{symbol}数据分析失败"


def analyze_rocket_launch_data(df, symbol="火箭发射"):
    """
    深度分析火箭发射数据，解析拉升幅度并按股票汇总统计
    
    :param df: 火箭发射数据DataFrame
    :param symbol: 异动类型名称
    :return: 分析后的数据字符串
    """
    try:
        if df is None or df.empty:
            return f"无{symbol}数据"
        
        logging.info(f"开始分析{symbol}数据，原始数据共{len(df)}条")
        
        # 数据预处理和清洗
        analysis_df = df.copy()
        
        # 第一步：数据解析与规整
        if '相关信息' in analysis_df.columns:
            # 解析相关信息列
            parsed_data = []
            
            for idx, row in analysis_df.iterrows():
                try:
                    info = str(row['相关信息']).strip()
                    if info and info != 'nan':
                        # 火箭发射的相关信息格式通常包含拉升幅度等信息
                        # 根据实际数据格式进行解析，这里假设包含拉升幅度信息
                        parts = info.split(',')
                        
                        # 尝试从相关信息中提取拉升幅度
                        # 可能的格式：涨幅%,价格,其他指标
                        lift_pct = 0
                        trigger_price = 0
                        
                        # 解析拉升幅度（假设第一个参数是拉升幅度）
                        if len(parts) > 0:
                            try:
                                # 可能是百分比格式或者小数格式
                                first_part = parts[0].strip()
                                if '%' in first_part:
                                    lift_pct = float(first_part.replace('%', ''))
                                else:
                                    lift_pct = float(first_part)
                                    # 如果是小数形式（如0.0869），转换为百分比
                                    if lift_pct < 1:
                                        lift_pct = lift_pct * 100
                            except ValueError:
                                lift_pct = 0
                        
                        # 解析触发价格（假设第二个参数是价格）
                        if len(parts) > 1:
                            try:
                                trigger_price = float(parts[1].strip())
                            except ValueError:
                                trigger_price = 0
                        
                        parsed_data.append({
                            '时间': row.get('时间', ''),
                            '代码': row.get('代码', ''),
                            '名称': row.get('名称', ''),
                            '异动类型': symbol,
                            '拉升幅度(%)': lift_pct,
                            '触发价格': trigger_price,
                            '原始信息': info
                        })
                        
                except (ValueError, IndexError) as e:
                    # 解析失败的记录跳过
                    logging.warning(f"解析{symbol}相关信息失败: {info}, 错误: {e}")
                    continue
            
            if not parsed_data:
                return f"无法解析{symbol}数据的相关信息"
            
            # 创建解析后的DataFrame
            parsed_df = pd.DataFrame(parsed_data)
            
            # 第二步：按股票代码汇总统计
            if '代码' in parsed_df.columns and not parsed_df.empty:
                # 按股票代码聚合，计算各种统计指标
                aggregated = parsed_df.groupby(['代码', '名称']).agg({
                    '拉升幅度(%)': ['count', 'max', 'sum', 'mean'],  # 发射次数、最大拉升、累计拉升、平均拉升
                    '触发价格': 'mean',                              # 平均触发价格
                    '时间': ['min', 'max']                           # 首次和末次发射时间
                }).round(2)
                
                # 重新整理列名
                aggregated.columns = ['发射次数', '最大拉升幅度(%)', '累计拉升幅度(%)', '平均拉升幅度(%)', '平均触发价格', '首次发射时间', '末次发射时间']
                aggregated = aggregated.reset_index()
                
                # 按发射次数和最大拉升幅度排序（优先发射次数，次要最大拉升幅度）
                aggregated = aggregated.sort_values(['发射次数', '最大拉升幅度(%)'], ascending=[False, False])
                
                # 计算总体统计
                total_launches = parsed_df.shape[0]
                unique_stocks = len(aggregated)
                max_single_lift = parsed_df['拉升幅度(%)'].max()
                total_lift_amount = parsed_df['拉升幅度(%)'].sum()
                
                # 生成分析报告
                analysis_summary = f"""
=== {symbol}数据深度分析 ===
📊 数据概况：
- 总发射记录：{total_launches}次
- 涉及股票数量：{unique_stocks}只
- 最大单次拉升：{max_single_lift:.2f}%
- 累计拉升总量：{total_lift_amount:.2f}%

🚀 TOP 10 {symbol}标的：
"""
                
                # 添加TOP 10详细信息
                for i, (idx, row) in enumerate(aggregated.head(10).iterrows(), 1):
                    analysis_summary += f"{i:2d}. {row['名称']}({row['代码']}): "
                    analysis_summary += f"发射{int(row['发射次数'])}次, "
                    analysis_summary += f"最大拉升{row['最大拉升幅度(%)']:.2f}%, "
                    analysis_summary += f"累计{row['累计拉升幅度(%)']:.2f}%, "
                    analysis_summary += f"均价{row['平均触发价格']:.2f}元\n"
                
                # 添加市场洞察
                if len(aggregated) > 0:
                    multi_launch_stocks = (aggregated['发射次数'] > 1).sum()
                    high_lift_stocks = (aggregated['最大拉升幅度(%)'] > 5).sum()  # 定义大于5%为高拉升
                    analysis_summary += f"""
💡 市场洞察：
- 平均单次拉升幅度：{total_lift_amount/total_launches:.2f}%
- 多次发射股票：{multi_launch_stocks}只 ({multi_launch_stocks/unique_stocks*100:.1f}%)
- 高拉升股票(>5%)：{high_lift_stocks}只 ({high_lift_stocks/unique_stocks*100:.1f}%)
- 市场拉升强度：{'高' if max_single_lift > 10 else '中等' if max_single_lift > 5 else '一般'}

"""
                
                # 拼接完整数据 (显示前50名)
                display_data = aggregated.head(50)
                result = analysis_summary + f"\n=== 前50名火箭发射统计 ===\n" + display_data.to_string(index=False)
                
                logging.info(f"{symbol}数据分析完成，聚合后{len(aggregated)}只股票，显示前{len(display_data)}名")
                return result
            
            else:
                # 如果聚合失败，返回解析后的数据
                display_data = parsed_df.head(50)
                return f"=== {symbol}解析数据（前50条） ===\n" + display_data.to_string(index=False)
                
        else:
            # 如果没有相关信息列，返回原始数据
            logging.warning(f"{symbol}数据中未找到'相关信息'列，返回原始数据")
            return f"=== {symbol}原始数据（前30条） ===\n" + df.head(30).to_string(index=False)
            
    except Exception as e:
        logging.error(f"分析{symbol}数据时发生错误: {e}")
        # 出错时返回简单版本
        if df is not None and not df.empty:
            return f"=== {symbol}数据（简化版前30条） ===\n" + df.head(30).to_string(index=False)
        return f"{symbol}数据分析失败"


def fetch_and_save_board_changes_data():
    """
    【新增】获取东方财富板块异动详情数据并保存。
    """
    try:
        logging.info("正在获取板块异动详情数据...")
        df = ak.stock_board_change_em()

        if df.empty:
            logging.warning("未能获取到板块异动详情数据。")
            return None, None

        # 过滤板块异动数据，只保留包含"大笔买入"类型的记录
        if '板块具体异动类型列表及出现次数' in df.columns:
            # 过滤出包含"大笔买入"的记录
            df = df[df['板块具体异动类型列表及出现次数'].str.contains('大笔买入', na=False)]
            logging.info(f"过滤后保留 {len(df)} 条包含'大笔买入'的板块异动数据。")
        else:
            logging.info(f"成功获取 {len(df)} 条板块异动详情数据。")

        if df.empty:
            logging.warning("过滤后无包含'大笔买入'的板块异动数据。")
            return None, None

        # 保存到本地
        now = datetime.now()
        date_folder = now.strftime('%Y-%m-%d')
        filename = f"{now.strftime('%H-%M')}_board_changes.csv"

        data_dir = os.path.join("fund_data", date_folder)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        filepath = os.path.join(data_dir, filename)
        df.to_csv(filepath, index=False, encoding='utf_8_sig')
        logging.info(f"板块异动详情数据已保存到: {filepath}")
        return df, filepath

    except Exception as e:
        logging.error(f"获取板块异动详情数据时发生错误: {e}")
        return None, None


def fetch_and_save_market_fund_flow_data():
    """
    获取东方财富大盘资金流数据并保存到CSV文件。
    """
    try:
        logging.info("正在获取大盘资金流数据...")
        df = ak.stock_market_fund_flow()

        if df.empty:
            logging.warning("未能获取到大盘资金流数据。")
            return None, None

        # 【修改】akshare返回的是历史数据，我们取最近10日的数据
        today_flow_df = df.tail(10)
        logging.info(f"成功获取到最近 {len(today_flow_df)} 日的大盘资金流数据。")

        # 保存到本地
        now = datetime.now()
        date_folder = now.strftime('%Y-%m-%d')
        filename = f"{now.strftime('%H-%M')}_market_fund_flow.csv"

        data_dir = os.path.join("fund_data", date_folder)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        filepath = os.path.join(data_dir, filename)
        today_flow_df.to_csv(filepath, index=False, encoding='utf_8_sig')
        logging.info(f"大盘资金流数据已保存到: {filepath}")
        return today_flow_df, filepath

    except Exception as e:
        logging.error(f"获取大盘资金流数据时发生错误: {e}")
        return None, None

def fetch_and_save_fund_flow_data():
    """
    获取同花顺"即时"个股资金流排名并保存到CSV文件，失败时尝试东方财富接口。
    """
    try:
        logging.info("正在获取同花顺即时个股资金流排名数据...")
        df = ak.stock_fund_flow_individual(symbol="即时")
        
        # 过滤ST和*ST股票 - 根据akshare接口文档，同花顺接口返回的是'股票简称'
        name_column = None
        if '股票简称' in df.columns:
            name_column = '股票简称'
        elif '名称' in df.columns:
            name_column = '名称'
        elif '股票名称' in df.columns:
            name_column = '股票名称'

        if name_column:
            df = df[~df[name_column].str.contains('ST|\\*ST', na=False)]
            logging.info(f"成功获取同花顺 {len(df)} 条个股资金流数据。")
        else:
            logging.warning("在同花顺数据中未找到股票名称列，跳过ST股票过滤。")
            logging.info(f"成功获取同花顺 {len(df)} 条个股资金流数据（未过滤）。")

        # 移除序号列（如果存在）
        if '序号' in df.columns:
            df = df.drop(columns=['序号'])
        
        # 保存到本地
        now = datetime.now()
        date_folder = now.strftime('%Y-%m-%d')
        filename = f"{now.strftime('%H-%M')}_fund_flow_ths.csv"

        data_dir = os.path.join("fund_data", date_folder)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        filepath = os.path.join(data_dir, filename)

        df.to_csv(filepath, index=False, encoding='utf_8_sig')
        logging.info(f"同花顺数据已保存到: {filepath}")
        return df, filepath

    except Exception as e:
        logging.error(f"获取同花顺个股资金流数据失败: {e}")
        logging.info("尝试获取东方财富个股资金流排名数据作为备份...")
        try:
            df = ak.stock_individual_fund_flow_rank(indicator="今日")
            # 过滤ST和*ST股票 - 东方财富接口返回 '名称'
            df = df[~df['名称'].str.contains('ST|\\*ST', na=False)]
            
            # 移除序号列（如果存在）
            if '序号' in df.columns:
                df = df.drop(columns=['序号'])
            
            logging.info(f"成功获取东方财富 {len(df)} 条个股资金流数据。")

            # 保存到本地
            now = datetime.now()
            date_folder = now.strftime('%Y-%m-%d')
            filename = f"{now.strftime('%H-%M')}_fund_flow_em_backup.csv"

            data_dir = os.path.join("fund_data", date_folder)
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)

            filepath = os.path.join(data_dir, filename)

            df.to_csv(filepath, index=False, encoding='utf_8_sig')
            logging.info(f"东方财富备份数据已保存到: {filepath}")
            return df, filepath

        except Exception as e2:
            logging.error(f"获取东方财富个股资金流数据失败: {e2}")
            return None, None


def fetch_and_save_sector_fund_flow_data():
    """
    获取同花顺"即时"行业资金流排名并保存到CSV文件，失败时尝试东方财富接口。
    """
    try:
        logging.info("正在获取同花顺即时行业资金流排名数据...")
        df = ak.stock_fund_flow_industry(symbol="即时")

        # 【新增】过滤掉无意义的行业概念
        if not df.empty:
            # 寻找行业名称列
            sector_name_col = None
            for col in ['行业', '板块', '名称', '行业名称', '板块名称']:
                if col in df.columns:
                    sector_name_col = col
                    break
            
            if sector_name_col:
                original_count = len(df)
                # 过滤掉无意义的行业
                df = df[df[sector_name_col].apply(is_meaningful_concept)]
                filtered_count = len(df)
                logging.info(f"行业数据过滤: 原始{original_count}条 -> 过滤后{filtered_count}条")
        
        # 只获取前50名
        df = df.head(50)

        logging.info(f"成功获取同花顺前 {len(df)} 条行业资金流数据。")
        logging.info(f"同花顺行业资金流数据列名: {list(df.columns)}")
        
        # 输出前几行数据样本用于调试
        if len(df) > 0:
            logging.info(f"同花顺行业资金流数据样本:\n{df.head(3).to_string()}")

        # 保存到本地
        now = datetime.now()
        date_folder = now.strftime('%Y-%m-%d')
        filename = f"{now.strftime('%H-%M')}_sector_fund_flow_ths.csv"

        data_dir = os.path.join("fund_data", date_folder)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        filepath = os.path.join(data_dir, filename)

        df.to_csv(filepath, index=False, encoding='utf_8_sig')
        logging.info(f"同花顺数据已保存到: {filepath}")
        return df, filepath

    except Exception as e:
        logging.error(f"获取同花顺行业资金流数据失败: {e}")
        logging.info("尝试获取东方财富行业资金流排名数据作为备份...")
        try:
            df = ak.stock_sector_fund_flow_rank(indicator="今日", sector_type="行业资金流")

            # 【新增】过滤掉无意义的行业概念
            if not df.empty:
                # 寻找行业名称列
                sector_name_col = None
                for col in ['行业', '板块', '名称', '行业名称', '板块名称']:
                    if col in df.columns:
                        sector_name_col = col
                        break
                
                if sector_name_col:
                    original_count = len(df)
                    # 过滤掉无意义的行业
                    df = df[df[sector_name_col].apply(is_meaningful_concept)]
                    filtered_count = len(df)
                    logging.info(f"东方财富行业数据过滤: 原始{original_count}条 -> 过滤后{filtered_count}条")

            # 只获取前50名
            df = df.head(50)

            logging.info(f"成功获取东方财富前 {len(df)} 条行业资金流数据。")

            # 保存到本地
            now = datetime.now()
            date_folder = now.strftime('%Y-%m-%d')
            filename = f"{now.strftime('%H-%M')}_sector_fund_flow_em_backup.csv"

            data_dir = os.path.join("fund_data", date_folder)
            if not os.path.exists(data_dir):
                os.makedirs(data_dir)

            filepath = os.path.join(data_dir, filename)

            df.to_csv(filepath, index=False, encoding='utf_8_sig')
            logging.info(f"东方财富备份数据已保存到: {filepath}")
            return df, filepath

        except Exception as e2:
            logging.error(f"获取东方财富行业资金流数据失败: {e2}")
            return None, None

def fetch_and_save_concept_fund_flow_data():
    """
    获取概念资金流排名数据，优先级：TPDOG > 东方财富 > 同花顺
    """
    # 第一优先级：尝试TPDOG概念资金流数据
    try:
        logging.info("正在获取TPDOG概念资金流排名数据...")

        # 获取TPDOG_TOKEN
        tpdog_token = os.getenv('TPDOG_TOKEN')
        if tpdog_token:
            # 调用TPDOG概念资金流接口
            import requests

            # 判断是否需要测试参数
            now_time = datetime.now().time()
            current_is_trading = (time(9, 30) <= now_time <= time(11, 30)) or (time(13, 0) <= now_time <= time(15, 0))

            if current_is_trading:
                url = f"https://www.tpdog.com/api/hs/current/bk_funds?bk_type=bkc&field=m_net&sort=2&token={tpdog_token}"
            else:
                url = f"https://www.tpdog.com/api/hs/current/bk_funds?bk_type=bkc&field=m_net&sort=2&t=1&token={tpdog_token}"
                logging.info("当前非交易时间，使用TPDOG测试参数")

            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                data = response.json()
                if data.get('code') in [1000, 1002] and data.get('content'):
                    df = pd.DataFrame(data['content'])
                    
                    # 检查是否为示例数据（非交易时间返回）
                    if len(df) == 1 and not is_trading_time():
                        logging.info("TPDOG非交易时间返回示例数据，跳过使用")
                        # 跳过使用示例数据，继续尝试其他数据源
                        pass  # 不返回，继续执行后续的数据源尝试
                    else:
                        # 标准化列名以匹配现有系统
                        if 'name' in df.columns:
                            df['概念'] = df['name']
                        if 'm_net' in df.columns:
                            df['主力净流入'] = df['m_net']
                        
                        # 过滤掉code列，避免提交给AI
                        if 'code' in df.columns:
                            df = df.drop('code', axis=1)
                            logging.info("TPDOG概念数据已过滤掉code列")

                        # 【新增】过滤掉无意义的概念
                        if not df.empty:
                            # 寻找概念名称列
                            concept_name_col = None
                            for col in ['概念', '名称', 'name', '概念名称']:
                                if col in df.columns:
                                    concept_name_col = col
                                    break
                            
                            if concept_name_col:
                                original_count = len(df)
                                # 过滤掉无意义的概念
                                df = df[df[concept_name_col].apply(is_meaningful_concept)]
                                filtered_count = len(df)
                                logging.info(f"TPDOG概念数据过滤: 原始{original_count}条 -> 过滤后{filtered_count}条")

                        # 只获取前50名
                        df = df.head(50)

                        logging.info(f"成功获取TPDOG前 {len(df)} 条概念资金流数据")

                        # 保存到本地
                        now = datetime.now()
                        date_folder = now.strftime('%Y-%m-%d')
                        filename = f"{now.strftime('%H-%M')}_concept_fund_flow_tpdog.csv"

                        data_dir = os.path.join("fund_data", date_folder)
                        if not os.path.exists(data_dir):
                            os.makedirs(data_dir)

                        filepath = os.path.join(data_dir, filename)
                        df.to_csv(filepath, index=False, encoding='utf_8_sig')
                        logging.info(f"TPDOG概念数据已保存到: {filepath}")
                        return df, filepath
                else:
                    logging.warning(f"TPDOG概念资金流API返回错误: {data}")
            else:
                logging.warning(f"TPDOG概念资金流API请求失败，状态码: {response.status_code}")
        else:
            logging.info("未配置TPDOG_TOKEN，跳过TPDOG数据源")

    except Exception as e:
        logging.error(f"获取TPDOG概念资金流数据失败: {e}")

    # 第二优先级：尝试东方财富概念资金流数据
    try:
        logging.info("正在获取东方财富概念资金流排名数据...")
        df = ak.stock_sector_fund_flow_rank(indicator="今日", sector_type="概念资金流")

        # 【新增】过滤掉无意义的概念
        if not df.empty:
            # 寻找概念名称列
            concept_name_col = None
            for col in ['概念', '板块', '名称', '概念名称', '板块名称']:
                if col in df.columns:
                    concept_name_col = col
                    break
            
            if concept_name_col:
                original_count = len(df)
                # 过滤掉无意义的概念
                df = df[df[concept_name_col].apply(is_meaningful_concept)]
                filtered_count = len(df)
                logging.info(f"东方财富概念数据过滤: 原始{original_count}条 -> 过滤后{filtered_count}条")

        # 【修改】只获取前50名
        df = df.head(50)

        logging.info(f"成功获取东方财富前 {len(df)} 条概念资金流数据。")

        # 保存到本地
        now = datetime.now()
        date_folder = now.strftime('%Y-%m-%d')
        filename = f"{now.strftime('%H-%M')}_concept_fund_flow_akshare.csv"

        data_dir = os.path.join("fund_data", date_folder)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        filepath = os.path.join(data_dir, filename)

        df.to_csv(filepath, index=False, encoding='utf_8_sig')
        logging.info(f"东方财富概念数据已保存到: {filepath}")
        return df, filepath

    except Exception as e:
        logging.error(f"获取东方财富概念资金流数据失败: {e}")

    # 第三优先级：尝试同花顺行业资金流数据作为备份
    try:
        logging.info("尝试获取同花顺行业资金流排名数据作为备份（作为概念资金流的近似替代）...")
        df = ak.stock_fund_flow_industry(symbol="即时")

        # 【新增】过滤掉无意义的概念/行业
        if not df.empty:
            # 寻找名称列
            name_col = None
            for col in ['行业', '板块', '名称', '行业名称', '板块名称']:
                if col in df.columns:
                    name_col = col
                    break
            
            if name_col:
                original_count = len(df)
                # 过滤掉无意义的概念/行业
                df = df[df[name_col].apply(is_meaningful_concept)]
                filtered_count = len(df)
                logging.info(f"同花顺概念备份数据过滤: 原始{original_count}条 -> 过滤后{filtered_count}条")

        # 【修改】只获取前50名
        df = df.head(50)

        logging.info(f"成功获取同花顺前 {len(df)} 条行业资金流数据（作为概念资金流备份）。")

        # 保存到本地
        now = datetime.now()
        date_folder = now.strftime('%Y-%m-%d')
        filename = f"{now.strftime('%H-%M')}_concept_fund_flow_ths_backup.csv"

        data_dir = os.path.join("fund_data", date_folder)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        filepath = os.path.join(data_dir, filename)

        df.to_csv(filepath, index=False, encoding='utf_8_sig')
        logging.info(f"同花顺备份数据已保存到: {filepath}")
        return df, filepath

    except Exception as e2:
        logging.error(f"获取同花顺行业资金流数据（概念备份）失败: {e2}")
        return None, None


def fetch_and_save_zt_pool_data():
    """
    获取东方财富"今日"涨停股池并保存到CSV文件，失败时记录错误。
    """
    try:
        logging.info("正在获取东方财富涨停股池数据...")
        now = datetime.now()
        date_str = now.strftime('%Y%m%d')

        # 获取交易日历
        trade_date_df = ak.tool_trade_date_hist_sina()
        trade_dates = trade_date_df['trade_date'].astype(str).str.replace('-', '')

        # 检查当天是否为交易日，如果不是，尝试最近5个交易日
        if date_str not in trade_dates.values:
            # 修复：正确处理日期比较和筛选最近交易日
            trade_dates_filtered = trade_dates[trade_dates < date_str]
            if trade_dates_filtered.empty:
                logging.warning("无可用交易日数据，跳过涨停股池获取。")
                return None, None
            # 获取最近5个交易日，按降序排列取前5个
            recent_trade_dates = trade_dates_filtered.tail(5).values[::-1]  # 取最后5个并倒序
            logging.info(f"当天非交易日，尝试最近交易日: {list(recent_trade_dates)}")
        else:
            recent_trade_dates = [date_str]

        df = None
        for try_date in recent_trade_dates:
            try:
                logging.info(f"尝试获取日期 {try_date} 的涨停股池数据...")
                df = ak.stock_zt_pool_em(date=try_date)
                logging.info(f"成功获取日期 {try_date} 的涨停股池数据，数据量: {len(df)} 条。")
                break
            except Exception as e:
                logging.error(f"获取日期 {try_date} 的涨停股池数据失败: {e}")
                continue

        if df is None or df.empty:
            logging.warning("所有尝试日期均无法获取涨停股池数据，跳过。")
            return None, None

        # 过滤ST和*ST股票 - 修复正则表达式
        df = df[~df['名称'].str.contains('ST|\\*ST', na=False)]
        logging.info(f"过滤ST后，剩余 {len(df)} 条涨停股池数据。")

        # 保存到本地
        date_folder = now.strftime('%Y-%m-%d')
        filename = f"{now.strftime('%H-%M')}_zt_pool.csv"

        data_dir = os.path.join("fund_data", date_folder)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        filepath = os.path.join(data_dir, filename)
        df.to_csv(filepath, index=False, encoding='utf_8_sig')
        logging.info(f"数据已保存到: {filepath}")
        return df, filepath

    except Exception as e:
        logging.error(f"获取东方财富涨停股池数据失败（全局错误）: {e}")
        logging.warning("无同花顺涨停股池接口可用，跳过备份。")
        return None, None


def analyze_zt_pool_statistics(zt_pool_df):
    """
    分析涨停股池统计信息，包括行业分布和连板数统计
    
    :param zt_pool_df: 涨停股池DataFrame
    :return: 统计分析字符串
    """
    try:
        if zt_pool_df is None or zt_pool_df.empty:
            return ""
        
        logging.info(f"开始分析涨停股池统计，数据量: {len(zt_pool_df)}条")
        
        # 寻找关键列名
        industry_col = None
        for col in ['行业', '所属板块', '所属行业', '板块', '板块名称']:
            if col in zt_pool_df.columns:
                industry_col = col
                break
        
        board_col = None
        for col in ['连板数', '连扳数', '板数', '几连板', '连板']:
            if col in zt_pool_df.columns:
                board_col = col
                break
        
        # 如果找不到连板数列，尝试从名称或其他字段推断
        if board_col is None:
            # 从涨停原因或其他字段中推断连板数
            for col in ['涨停原因', '封单资金', '炸板次数']:
                if col in zt_pool_df.columns:
                    # 这里可以根据实际数据格式进行连板数推断
                    break
        
        statistics = []
        
        # 1. 行业统计
        if industry_col:
            industry_counts = zt_pool_df[industry_col].value_counts()
            if not industry_counts.empty:
                # 涨停行业最多的
                max_count = industry_counts.iloc[0]
                top_industries = industry_counts[industry_counts == max_count]
                
                if len(top_industries) == 1:
                    top_industry = top_industries.index[0]
                    statistics.append(f"涨停行业最多的：{top_industry}（{max_count}只）")
                else:
                    # 多个行业并列，显示前3名
                    industries_list = []
                    for industry, count in top_industries.head(3).items():
                        industries_list.append(f"{industry}（{count}只）")
                    industries_str = "、".join(industries_list)
                    statistics.append(f"涨停行业最多的：{industries_str}")
        
        # 2. 连板数统计
        if board_col:
            # 清理连板数数据，确保为数值类型
            board_data = zt_pool_df[board_col].copy()
            
            # 尝试转换为数值类型
            try:
                # 如果是字符串，尝试提取数字
                if board_data.dtype == 'object':
                    board_data = pd.to_numeric(board_data.astype(str).str.extract(r'(\d+)')[0], errors='coerce')
                else:
                    board_data = pd.to_numeric(board_data, errors='coerce')
                
                # 过滤有效数据
                valid_board_data = board_data.dropna()
                
                if not valid_board_data.empty:
                    # 将连板数与行业数据合并进行统计
                    if industry_col:
                        df_for_analysis = zt_pool_df[[industry_col, board_col]].copy()
                        df_for_analysis['连板数'] = board_data
                        df_for_analysis = df_for_analysis.dropna()
                        
                        # 连板数最多行业（所有连板数中占比最多的行业）
                        if not df_for_analysis.empty:
                            board_industry_counts = df_for_analysis[industry_col].value_counts()
                            if not board_industry_counts.empty:
                                max_count = board_industry_counts.iloc[0]
                                top_industries = board_industry_counts[board_industry_counts == max_count]
                                
                                if len(top_industries) == 1:
                                    top_industry = top_industries.index[0]
                                    statistics.append(f"连板数最多行业：{top_industry}（{max_count}只）")
                                else:
                                    # 多个行业并列，显示前3名
                                    industries_list = []
                                    for industry, count in top_industries.head(3).items():
                                        industries_list.append(f"{industry}（{count}只）")
                                    industries_str = "、".join(industries_list)
                                    statistics.append(f"连板数最多行业：{industries_str}")
                        
                        # 按连板数分别统计
                        for board_num in [1, 2, 3, 4, 5]:
                            board_subset = df_for_analysis[df_for_analysis['连板数'] == board_num]
                            if not board_subset.empty:
                                board_industry_counts = board_subset[industry_col].value_counts()
                                if not board_industry_counts.empty:
                                    # 获取最高数量
                                    max_count = board_industry_counts.iloc[0]
                                    
                                    # 找出所有并列第一的行业
                                    top_industries = board_industry_counts[board_industry_counts == max_count]
                                    
                                    # 如果只有一个行业
                                    if len(top_industries) == 1:
                                        top_industry = top_industries.index[0]
                                        if board_num == 1:
                                            desc = f"首板最多行业：{top_industry}（{max_count}只）"
                                        else:
                                            desc = f"{board_num}板连板数最多行业：{top_industry}（{max_count}只）"
                                    else:
                                        # 多个行业并列，显示前3名或全部（如果少于3个）
                                        industries_list = []
                                        for industry, count in top_industries.head(3).items():
                                            industries_list.append(f"{industry}（{count}只）")
                                        
                                        industries_str = "、".join(industries_list)
                                        if board_num == 1:
                                            desc = f"首板最多行业：{industries_str}"
                                        else:
                                            desc = f"{board_num}板连板数最多行业：{industries_str}"
                                    
                                    statistics.append(desc)
                    
            except Exception as e:
                logging.warning(f"连板数统计分析失败: {e}")
        
        # 如果没有连板数列，但有行业数据，提供基础统计
        elif industry_col and not statistics:
            industry_counts = zt_pool_df[industry_col].value_counts()
            if not industry_counts.empty:
                # 只提供涨停行业统计
                max_count = industry_counts.iloc[0]
                top_industries = industry_counts[industry_counts == max_count]
                
                if len(top_industries) == 1:
                    top_industry = top_industries.index[0]
                    statistics.append(f"涨停行业最多的：{top_industry}（{max_count}只）")
                else:
                    # 多个行业并列，显示前3名
                    industries_list = []
                    for industry, count in top_industries.head(3).items():
                        industries_list.append(f"{industry}（{count}只）")
                    industries_str = "、".join(industries_list)
                    statistics.append(f"涨停行业最多的：{industries_str}")
        
        # 生成最终输出
        if statistics:
            result = "\n".join(statistics) + "\n"
            logging.info(f"涨停股池统计分析完成，生成{len(statistics)}项统计")
            return result
        else:
            logging.warning("未能生成涨停股池统计信息，可能缺少关键字段")
            return ""
            
    except Exception as e:
        logging.error(f"分析涨停股池统计时发生错误: {e}")
        return ""


def fetch_and_save_ths_fund_flow_data():
    """
    获取同花顺"即时"前100名个股资金流并保存到CSV文件，过滤涨幅限制。
    """
    try:
        logging.info("正在获取同花顺即时个股资金流排名数据...")
        df = ak.stock_fund_flow_individual(symbol="即时")

        # 检查数据结构并选择正确的列名进行过滤
        name_column = None
        if '名称' in df.columns:
            name_column = '名称'
        elif '股票简称' in df.columns:
            name_column = '股票简称'
        elif '股票名称' in df.columns:
            name_column = '股票名称'

        # 过滤ST和*ST股票 - 修复正则表达式和列名
        if name_column:
            df = df[~df[name_column].str.contains('ST|\\*ST', na=False)]
            logging.info(f"成功获取 {len(df)} 条同花顺个股资金流数据。")
        else:
            logging.warning("未找到股票名称列，跳过ST股票过滤")
            logging.info(f"成功获取 {len(df)} 条同花顺个股资金流数据。")

        # 过滤涨幅：300开头>19.9%，00或60开头>9.9%
        df['股票代码'] = df['股票代码'].astype(str)
        df['涨跌幅'] = df['涨跌幅'].str.rstrip('%').astype(float)
        filtered_df = df[
            ~(
                    (df['股票代码'].str.startswith('300') & (df['涨跌幅'] > 19.9)) |
                    ((df['股票代码'].str.startswith(('00', '60'))) & (df['涨跌幅'] > 9.9))
            )
        ].head(100)
        
        # 移除序号列（如果存在）
        if '序号' in filtered_df.columns:
            filtered_df = filtered_df.drop(columns=['序号'])
            
        logging.info(f"过滤后保留 {len(filtered_df)} 条数据。")

        # 保存到本地
        now = datetime.now()
        date_folder = now.strftime('%Y-%m-%d')
        filename = f"{now.strftime('%H-%M')}_ths_fund_flow.csv"

        data_dir = os.path.join("fund_data", date_folder)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        filepath = os.path.join(data_dir, filename)

        filtered_df.to_csv(filepath, index=False, encoding='utf_8_sig')
        logging.info(f"数据已保存到: {filepath}")
        return filtered_df, filepath

    except Exception as e:
        logging.error(f"获取或保存同花顺个股资金流数据时发生错误: {e}")
        return None, None


def fetch_and_save_lhb_data():
    """
    获取东方财富龙虎榜详情数据并保存到CSV文件。
    这是盘后分析的核心数据。
    """
    try:
        logging.info("正在获取盘后龙虎榜详情数据...")
        now = datetime.now()
        date_str_today = now.strftime('%Y%m%d')

        # --- 修复逻辑：获取最近的交易日 ---
        trade_date_df = ak.tool_trade_date_hist_sina()
        trade_dates = pd.to_datetime(trade_date_df['trade_date']).dt.strftime('%Y%m%d')
        # 找到不晚于今天的最近一个交易日
        last_trade_date = trade_dates[trade_dates <= date_str_today].iloc[-1]
        logging.info(f"定位到最近交易日为: {last_trade_date} 以获取龙虎榜数据。")

        # --- 修复逻辑：使用正确的参数名 start_date 和 end_date ---
        df = ak.stock_lhb_detail_em(start_date=last_trade_date, end_date=last_trade_date)

        if df.empty:
            logging.warning(f"日期 {last_trade_date} 未获取到龙虎榜数据，可能为非交易日或数据未更新。")
            return None, None

        # 【修改】过滤ST和*ST股票
        if '名称' in df.columns:
            df = df[~df['名称'].str.contains('ST|\\*ST', na=False)]

        # 【修改】去掉不需要的列
        columns_to_drop = ['上榜后1日', '上榜后2日', '上榜后5日', '上榜后10日']
        df = df.drop(columns=columns_to_drop, errors='ignore')

        logging.info(f"成功获取 {len(df)} 条龙虎榜数据（已过滤）。")

        # 保存到本地
        date_folder = now.strftime('%Y-%m-%d')
        # 文件名明确标识为盘后数据
        filename = f"{now.strftime('%H-%M')}_lhb_detail.csv"

        data_dir = os.path.join("fund_data", date_folder)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        filepath = os.path.join(data_dir, filename)
        df.to_csv(filepath, index=False, encoding='utf_8_sig')
        logging.info(f"龙虎榜数据已保存到: {filepath}")
        return df, filepath

    except Exception as e:
        logging.error(f"获取龙虎榜数据时发生错误: {e}")
        return None, None


def fetch_and_save_lhb_jgmmtj_data():
    """
    获取东方财富“机构买卖每日统计”作为盘后龙虎榜核心数据，并保存到CSV文件。
    """
    try:
        logging.info("正在获取盘后核心数据：龙虎榜-机构买卖每日统计...")
        now = datetime.now()
        date_str_today = now.strftime('%Y%m%d')

        # --- 修复逻辑：获取最近的交易日 ---
        trade_date_df = ak.tool_trade_date_hist_sina()
        trade_dates = pd.to_datetime(trade_date_df['trade_date']).dt.strftime('%Y%m%d')
        # 找到不晚于今天的最近一个交易日
        last_trade_date = trade_dates[trade_dates <= date_str_today].iloc[-1]
        logging.info(f"定位到最近交易日为: {last_trade_date} 以获取机构买卖统计数据。")

        # 接口需要当天的日期作为 start_date 和 end_date
        df = ak.stock_lhb_jgmmtj_em(start_date=last_trade_date, end_date=last_trade_date)

        if df.empty:
            logging.warning(f"日期 {last_trade_date} 未获取到龙虎榜-机构买卖统计数据，可能为非交易日或数据未更新。")
            return None, None

        logging.info(f"成功获取 {len(df)} 条龙虎榜-机构买卖统计数据。")

        # 保存到本地
        date_folder = now.strftime('%Y-%m-%d')
        # 文件名明确标识为盘后数据
        filename = f"{now.strftime('%H-%M')}_lhb_jgmmtj.csv"

        data_dir = os.path.join("fund_data", date_folder)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        filepath = os.path.join(data_dir, filename)
        df.to_csv(filepath, index=False, encoding='utf_8_sig')
        logging.info(f"龙虎榜-机构买卖统计数据已保存到: {filepath}")
        return df, filepath

    except Exception as e:
        logging.error(f"获取龙虎榜-机构买卖统计数据时发生错误: {e}")
        return None, None

def fetch_and_save_ths_big_deal_data():
    """
    获取同花顺大单追踪数据并保存到CSV文件。
    """
    try:
        logging.info("正在获取同花顺大单追踪数据...")
        df = ak.stock_fund_flow_big_deal()

        # 检查数据结构并选择正确的列名进行过滤
        name_column = None
        if '股票简称' in df.columns:
            name_column = '股票简称'
        elif '名称' in df.columns:
            name_column = '名称'
        elif '股票名称' in df.columns:
            name_column = '股票名称'

        # 过滤ST和*ST股票 - 修复正则表达式和列名
        if name_column:
            df = df[~df[name_column].str.contains('ST|\\*ST', na=False)]
            logging.info(f"成功获取 {len(df)} 条同花顺大单追踪数据。")
        else:
            logging.warning("未找到股票名称列，跳过ST股票过滤")
            logging.info(f"成功获取 {len(df)} 条同花顺大单追踪数据。")

        # 保存到本地
        now = datetime.now()
        date_folder = now.strftime('%Y-%m-%d')
        filename = f"{now.strftime('%H-%M')}_ths_big_deal.csv"

        data_dir = os.path.join("fund_data", date_folder)
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)

        filepath = os.path.join(data_dir, filename)

        df.to_csv(filepath, index=False, encoding='utf_8_sig')
        logging.info(f"数据已保存到: {filepath}")
        return df, filepath

    except Exception as e:
        logging.error(f"获取或保存同花顺大单追踪数据时发生错误: {e}")
        return None, None


def analyze_ths_big_deal_data(ths_big_deal_df, top_n=200):
    """
    按股票代码聚合同花顺大单追踪数据，计算大单净流入、买入卖出额等指标
    
    :param ths_big_deal_df: 完整的同花顺大单追踪数据
    :param top_n: 返回的top数量，默认200
    :return: 分析后的数据字符串
    """
    try:
        if ths_big_deal_df is None or ths_big_deal_df.empty:
            return "无同花顺大单追踪数据"
        
        logging.info(f"开始分析同花顺大单追踪数据，原始数据共{len(ths_big_deal_df)}条")
        
        # 数据预处理和清洗
        df = ths_big_deal_df.copy()
        
        # 寻找关键列名
        code_col = None
        for col in ['股票代码', '代码', 'code', '证券代码']:
            if col in df.columns:
                code_col = col
                break
        
        name_col = None
        for col in ['股票简称', '名称', '股票名称', 'name']:
            if col in df.columns:
                name_col = col
                break
        
        amount_col = None
        for col in ['成交额', '成交金额', '金额', '大单金额']:
            if col in df.columns:
                amount_col = col
                break
        
        nature_col = None
        for col in ['大单性质', '性质', '买卖性质', '方向']:
            if col in df.columns:
                nature_col = col
                break
        
        change_col = None
        for col in ['涨跌幅', '涨跌', '涨幅', '今日涨跌幅']:
            if col in df.columns:
                change_col = col
                break
        
        time_col = None
        for col in ['成交时间', '时间', '交易时间']:
            if col in df.columns:
                time_col = col
                break
        
        if not code_col or not name_col or not amount_col or not nature_col:
            logging.warning("缺少必要列，使用原始数据显示")
            return f"=== 同花顺大单追踪前{top_n}名数据 ===\n" + df.head(top_n).to_string(index=False)
        
        # 数据类型转换
        df[amount_col] = pd.to_numeric(df[amount_col], errors='coerce').fillna(0)
        if change_col:
            df[change_col] = pd.to_numeric(df[change_col], errors='coerce').fillna(0)
        
        # 转换成交额为万元
        df['成交额_万元'] = df[amount_col] / 10000
        
        # 按股票代码和成交时间排序，获取每只股票的最新状态
        if time_col:
            df = df.sort_values([code_col, time_col])
        
        # 按股票代码聚合数据
        aggregation_dict = {
            '成交额_万元': 'sum',  # 总成交额
        }
        
        # 添加涨跌幅的最新值
        if change_col:
            aggregation_dict[change_col] = 'last'  # 获取最新的涨跌幅
        
        # 按股票分组聚合
        grouped = df.groupby([code_col, name_col]).agg(aggregation_dict).reset_index()
        
        # 计算买入和卖出额
        buy_amounts = df[df[nature_col].str.contains('买', na=False)].groupby(code_col)['成交额_万元'].sum()
        sell_amounts = df[df[nature_col].str.contains('卖', na=False)].groupby(code_col)['成交额_万元'].sum()
        
        # 合并买入卖出数据
        grouped['大单买入额(万元)'] = grouped[code_col].map(buy_amounts).fillna(0)
        grouped['大单卖出额(万元)'] = grouped[code_col].map(sell_amounts).fillna(0)
        
        # 计算大单净流入
        grouped['大单净流入(万元)'] = grouped['大单买入额(万元)'] - grouped['大单卖出额(万元)']
        
        # 重命名列
        grouped = grouped.rename(columns={
            code_col: '股票代码',
            name_col: '股票简称',
            '成交额_万元': '大单总成交额(万元)'
        })
        
        if change_col:
            grouped = grouped.rename(columns={change_col: '最新涨跌幅'})
            # 格式化涨跌幅
            grouped['最新涨跌幅'] = grouped['最新涨跌幅'].apply(lambda x: f"{x:.2f}%" if pd.notna(x) else "0.00%")
        else:
            grouped['最新涨跌幅'] = "N/A"
        
        # 按大单净流入排序
        grouped = grouped.sort_values('大单净流入(万元)', ascending=False)
        
        # 选择前N名
        top_data = grouped.head(top_n)
        
        # 四舍五入数值列
        for col in ['大单净流入(万元)', '大单总成交额(万元)', '大单买入额(万元)', '大单卖出额(万元)']:
            if col in top_data.columns:
                top_data[col] = top_data[col].round(2)
        
        # 计算统计信息
        total_records = len(df)
        unique_stocks = len(grouped)
        total_net_inflow = grouped['大单净流入(万元)'].sum()
        positive_stocks = (grouped['大单净流入(万元)'] > 0).sum()
        
        # 生成分析报告
        analysis_summary = f"""
=== 同花顺大单追踪数据分析摘要 ===
📊 数据概况：
- 原始交易记录：{total_records}条
- 涉及股票数量：{unique_stocks}只
- 大单净流入总额：{total_net_inflow:,.2f}万元
- 净流入为正：{positive_stocks}只 ({positive_stocks/unique_stocks*100:.1f}%)

🏆 TOP 10 大单净流入：
"""
        
        # 添加TOP 10详细信息
        for i, (idx, row) in enumerate(top_data.head(10).iterrows(), 1):
            analysis_summary += f"{i:2d}. {row['股票简称']}({row['股票代码']}): "
            analysis_summary += f"净流入{row['大单净流入(万元)']:,.2f}万元, "
            analysis_summary += f"涨跌幅{row['最新涨跌幅']}\n"
        
        # 添加市场洞察
        if len(top_data) > 0:
            top10_concentration = top_data.head(10)['大单净流入(万元)'].sum() / total_net_inflow if total_net_inflow != 0 else 0
            analysis_summary += f"""
💡 市场洞察：
- TOP10资金集中度：{top10_concentration:.1%}
- 平均大单规模：{grouped['大单总成交额(万元)'].mean():,.2f}万元
- 市场大单活跃度：{'高' if total_records > 1000 else '中等' if total_records > 500 else '一般'}

"""
        
        # 重新排列列顺序
        columns_order = ['股票代码', '股票简称', '大单净流入(万元)', '大单总成交额(万元)', 
                        '大单买入额(万元)', '大单卖出额(万元)', '最新涨跌幅']
        display_data = top_data[columns_order]
        
        # 拼接完整数据
        result = analysis_summary + f"\n=== 前{top_n}名大单追踪汇总 ===\n" + display_data.to_string(index=False)
        
        logging.info(f"同花顺大单追踪数据分析完成，聚合后{len(grouped)}只股票，显示前{len(top_data)}名")
        return result
        
    except Exception as e:
        logging.error(f"分析同花顺大单追踪数据时发生错误: {e}")
        # 出错时返回简单版本
        if ths_big_deal_df is not None and not ths_big_deal_df.empty:
            return f"=== 同花顺大单追踪前{top_n}名数据（简化版） ===\n" + ths_big_deal_df.head(top_n).to_string(index=False)
        return "同花顺大单追踪数据分析失败"



def read_book_files(folder_path="book"):
    """
    读取 'book' 文件夹下所有 .txt 文件的内容。
    """
    logging.info(f"正在从 '{folder_path}' 文件夹读取背景资料...")
    if not os.path.exists(folder_path):
        logging.warning(f"'{folder_path}' 文件夹不存在，将不加载任何背景资料。")
        os.makedirs(folder_path)
        return ""

    all_content = []
    try:
        for filename in os.listdir(folder_path):
            if filename.endswith(".txt"):
                filepath = os.path.join(folder_path, filename)
                with open(filepath, 'r', encoding='utf-8') as f:
                    content = f.read()
                    all_content.append(f"--- 来自文件: {filename} ---\n{content}\n")
                    logging.info(f"已加载资料: {filename}")

        if not all_content:
            logging.warning(f"'{folder_path}' 文件夹中没有找到 .txt 文件。")
            return ""

        return "\n".join(all_content)
    except Exception as e:
        logging.error(f"读取背景资料文件时出错: {e}")
        return ""

# ==============================================================================
# 模块三：完全模块化的动态智能Prompt (The Dynamic Prompt)
# ==============================================================================

def analyze_with_gemini(
    dynamic_stock_report,
    dynamic_sector_concept_report,  # <--- 新增参数
    news_data,
    static_stock_data,
    static_sector_data,
    static_concept_data,
    static_zt_pool_data,
    static_board_changes_data,
    static_market_movers_data,
    static_tech_indicators_data,
    static_ths_fund_flow_data,
    static_ths_big_deal_data
):
    """
    模块三：重构后的analyze_with_gemini函数
    接收所有已经处理和格式化好的字符串变量，使用f-string将它们注入到清晰、易于维护的Prompt模板中。

    :param dynamic_stock_report: 动态个股资金流分析报告字符串
    :param dynamic_sector_concept_report: 动态行业概念资金流分析报告字符串
    :param news_data: 新闻数据字符串
    :param static_stock_data: 静态个股资金流数据字符串
    :param static_sector_data: 静态行业资金流数据字符串
    :param static_concept_data: 静态概念资金流数据字符串
    :param static_zt_pool_data: 静态涨停股池数据字符串
    :param static_board_changes_data: 静态板块异动数据字符串
    :param static_market_movers_data: 静态盘口异动数据字符串
    :param static_tech_indicators_data: 静态技术指标数据字符串
    :param static_ths_fund_flow_data: 静态同花顺个股资金流数据字符串
    :param static_ths_big_deal_data: 静态同花顺大单追踪数据字符串
    """
    logging.info(f"准备调用 {GEMINI_MODEL_NAME} 进行动态智能分析...")

    # 构建完全使用变量的动态智能Prompt模板
    prompt = f"""
你是一位顶级的A股超短线交易大师，你的交易哲学和分析框架深度融合了“华东大导弹”、“赵老哥”、“涅盘重升”、“著名刺客”、“孤独牛背”、“92科比”、“龙飞虎”、“凡倍无名”、“小鳄鱼”、"作手新一"、"方新侠"、"佛山无影脚"、"瑞鹤仙"、"退学炒股"、"乔帮主"、"Asking (A神)"、	"陈小群" 等多位顶级游资的核心思想。你将严格遵循以下心法进行思考和分析：

*   **情绪周期为王：** 你的一切分析都始于对当前市场情绪的判断（是混沌、启动、高潮还是退潮？），以及“赚钱效应”和“亏钱效应”的量化。
*   **龙头战法是纲：** 你的眼中只有龙头。你坚信市场的核心利润集中在总龙头、板块龙头和人气核心股上。“二板定龙头”、“有新题材、抛旧题材”是你铁的纪律。
*   **主线题材是战场：** 你只在当时市场最强、共识度最高的主线题材中寻找战机。非主流、非热点、无人气的板块你绝不参与。
*   **换手决定高度：** 你偏爱换手充分的龙头，对连续缩量一字板的个股保持高度警惕，因为“没爆量的都不能说是龙头”。
*   **模式化交易：** 你擅长捕捉高确定性的交易模式，尤其是“弱转强”、“高低切换”、“龙头首阴反包”、“补涨龙”等。
*   **纪律重于预测：** 你从不预测，只跟随。你的操作基于信号和模式，纪律是你的生命线。你深刻理解“空仓是最高级别的防守”。

你所有的分析和建议，都必须围绕**《金字塔堡垒》**的作战框架展开。请将以下结构内化为你的思维模型：
--资金账户结构
总资金 (TC): 1,000,000 元
攻坚部队 (AF) 总预算: 150,000 元 (15% TC)
主战军团 (MBG) 总预算: 500,000 元 (50% TC)
多点潜伏哨 (SO) 总预算: 200,000 元 (20% TC)
现金预备队 (CR) 初始资金: 150,000 元 (15% TC)

# **第一部分：核心动态分析报告 (!!! 最高优先级分析依据 !!!)**
*这是当前资金流的动态变化雷达图。你的所有决策都必须基于对这份报告的深度解读，尤其是对"排名变化"列的分析。*

## 个股资金流动态分析
{dynamic_stock_report}

# 【新增】行业与概念战场动态分析
## 行业与概念动态分析报告
*这是当前行业与概念板块的动态变化雷达图，用于识别主战场、新兴热点和板块轮动路径。*
{dynamic_sector_concept_report}

# 第二部分：实时盘面数据（这是你分析的重要补充依据）
接下来，这是最新的市场数据，反映了当前市场的真实资金动向和热点

## 实时新闻与资讯
*这是市场最新发生的故事，是寻找题材催化剂的关键。*
{news_data}
## A股个股资金流排名（前200名）
```
{static_stock_data}
```

## 行业资金流排名（前200名）
```
{static_sector_data}
```

## 概念资金流排名（前200名）
```
{static_concept_data}
```

## 涨停股池（前200名）
*关注最高板，连扳高度，炸板等情况判断市场赚钱效应。*
```
{static_zt_pool_data}
```

## 板块异动详情
*这揭示了哪些板块在盘中出现了集中性的拉升或打压，是识别主线和支线的重要参考。*
```
{static_board_changes_data}
```

## 盘口异动数据
*这反映了盘中最激烈的多空博弈瞬间，是发现个股“弱转强”或“强转弱”信号的雷达。*
```
{static_market_movers_data}
```

## 技术形态选股池
*从纯技术角度筛选出的强势个股，可以与主线逻辑交叉验证，寻找共振点。*
```
{static_tech_indicators_data}
```

## 同花顺个股资金流排名（前200名，过滤300开头涨幅>19.9%、00或60开头涨幅>9.9%）
*注意：这份数据已过滤掉涨停股（300开头>19.9%，主板>9.9%）。这非常关键，我们寻找的是尚未涨停但资金已大力介入的潜力股，而不是已经封死涨停的股票。*
```
{static_ths_fund_flow_data}
```

## 同花顺大单追踪（前200名）
```
{static_ths_big_deal_data}
```

你的任务：生成《今日战场分析与作战预案》
请严格按照以下结构，生成你的报告：
第一部分：战场态势研判
市场灵魂与情绪诊断:
当前情绪周期: (冰点/混沌/主升/高位震荡/退潮？) 给出判断依据（如赚钱/亏钱效应、连板高度等）。
市场总龙头: 谁是当前市场的灵魂？是【倍加洁】这样的情绪标，还是另有其人？它的走势对市场意味着什么？
主战场分析:
今日主攻方向: 明确指出今天的主线战场是哪个板块。必须结合【资金断层】、【板块效应】和【新闻催化剂】进行论证。
主线健康度评估: 这个主线的梯队是否完整？（有无龙头、中军、后排助攻？）
第二部分：“攻坚部队(AF)”目标锁定分析
核心任务: 为【攻坚部队】寻找唯一的攻击目标。
候选池: 列出所有符合AF-U1 (最高板) 和 AF-U2 (资金核心) 的候选者。
【冠军对决 - 详细分析过程】:
你必须展示你详细的决策思考过程。 运用**“题材纯度(50%)、市场身位(30%)、资金质量(20%)”**三大原则，对候选池中的强者（如【东信和平】vs【张江高科】vs【倍加洁】）进行“华山论剑”。
详细阐述你为什么选择A而不是B，你的理由是什么。
最终建议:
AF目标: [股票代码] [股票名称]
核心理由: (一句话总结你的决策结果)
第三部分：“主战军团(MBG)”与“潜伏哨(SO)”作战配置建议
核心任务: 基于已确定的主线和支线，为MBG和SO提供一个经过优先级排序的配置方案。
主战军团 (MBG) 配置方案:
选股原则: 围绕AF目标所在的主线，以及市场最强的支线，配置梯队核心和趋势中军。
建议列表 (请按重要性降序排列):
[股票代码] [股票名称]: (理由: 例如，市场资金总龙头，主线核心中军)
[股票代码] [股票名称]: (理由: 例如，最强汽车支线的3板龙头)
... (列出5-8个)
多点潜伏哨 (SO) 配置方案:
选股原则: 寻找具备“预期差”的低位异动股，优先选择有明确“强资金介入”信号的标的。
建议列表 (请按性价比降序排列):
[股票代码] [股票名称]: (理由: 例如，底部首板+主力资金净占比超50%，是最高性价比的潜伏选择)
[股票代码] [股票名称]: (理由: 例如，PEEK新题材的低位火种，值得用彩票仓位试错)
... (列出5-10个)
第四部分：最终交易计划汇总
请将以上所有分析，最终汇总成一份清晰的、包含具体金额的模拟买入计划。这部分要求格式化，便于我参考执行。
模拟买入计划
归属单元	股票代码	股票名称	买入金额(元)	入选核心理由
AF	...	...	150,000	(例如：冠军对决胜者，市场最强主线核心)
MBG	...	...	83300	(例如：主线核心中军)
MBG	...	...	83300	(例如：最强支线龙头)
...	...	...	...	...
SO	...	...	30000	(例如：最高性价比潜伏)
...	...	...	...	...
[结束命令]
请以专业、自信且条理清晰的风格，生成你的分析报告。
"""

    if ENABLE_DETAILED_LOGGING:
        logging.info(f"--- 发送给 {GEMINI_MODEL_NAME} 的完整提示词 ---")
        logging.info(prompt)
        logging.info("---------------------------------")

    try:
        # 在每次分析前更新API密钥
        API_KEY = get_current_api_key()
        genai.configure(api_key=API_KEY)
        model = genai.GenerativeModel(GEMINI_MODEL_NAME)
        logging.info("正在向Gemini发送长上下文请求...")
        response = model.generate_content(prompt)
        logging.info("已收到Gemini的响应。")

        print("\n" + "=" * 50)
        print(f"📈 Gemini 2.5 Pro 长上下文分析报告 📈")
        print("=" * 50)
        print(response.text)
        print("=" * 50 + "\n")

        # 保存Gemini响应
        now = datetime.now()
        date_folder = now.strftime('%Y-%m-%d')
        response_dir = os.path.join("Response", "gemini_responses", date_folder)
        if not os.path.exists(response_dir):
            os.makedirs(response_dir)

        response_filename = f"{now.strftime('%H-%M')}_gemini_response.txt"
        response_filepath = os.path.join(response_dir, response_filename)

        with open(response_filepath, 'w', encoding='utf-8') as f:
            f.write(response.text)
        logging.info(f"Gemini响应已保存到: {response_filepath}")
        # 盘中分析的股票池，作为即时买入信号
        parse_and_save_gemini_watchlist_new(response.text, "D:/gemini_signal.json")

    except Exception as e:
        logging.error(f"调用Gemini API时发生错误: {e}")
        if "API key not valid" in str(e):
            logging.error("请检查你的 ..env 文件中的 GEMINI_API_KEY 是否正确。")






def job():
    """
    【已修改】定义一个完整的任务，包括获取所有新增数据和分析。
    """
    global TASK_RUNNING

    # 检查是否有任务正在执行
    if TASK_RUNNING:
        logging.info("上一个任务仍在执行中，跳过本次任务...")
        return

    # 设置任务执行状态
    TASK_RUNNING = True

    try:
        if ENABLE_TRADING_TIME_CHECK and not is_trading_time():
            current_time = datetime.now().strftime('%H:%M:%S')
            msg = f"非交易时间（当前时间: {current_time}），跳过任务执行。交易时间: 9:25-11:30, 13:00-15:00"
            logging.info(msg)
            print(msg)
            return

        print(f"\n[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 开始执行新一轮任务...")

        # 获取所有新数据
        fund_flow_df, _ = fetch_and_save_fund_flow_data()
        sector_fund_flow_df, _ = fetch_and_save_sector_fund_flow_data()
        concept_fund_flow_df, _ = fetch_and_save_concept_fund_flow_data()
        zt_pool_df, _ = fetch_and_save_zt_pool_data()
        ths_fund_flow_df, _ = fetch_and_save_ths_fund_flow_data()
        ths_big_deal_df, _ = fetch_and_save_ths_big_deal_data()

        # 获取新闻、盘口异动、板块异动、技术指标数据
        news_data = fetch_and_save_news_data()
        market_movers_data = fetch_and_save_market_movers_data()
        board_changes_df, _ = fetch_and_save_board_changes_data()
        tech_indicators_dict = fetch_and_save_technical_indicators_data()

        if fund_flow_df is not None:
            # 模块一：构建新数据字典并管理缓存
            new_data_dict = {
                'fund_flow_df': fund_flow_df,
                'sector_fund_flow_df': sector_fund_flow_df,
                'concept_fund_flow_df': concept_fund_flow_df,
                'zt_pool_df': zt_pool_df,
                'ths_fund_flow_df': ths_fund_flow_df,
                'ths_big_deal_df': ths_big_deal_df,
                'board_changes_df': board_changes_df,
                'tech_indicators_dict': tech_indicators_dict,
                'news_data': news_data,
                'market_movers_data': market_movers_data
            }

            # 加载缓存以获取历史数据
            cache = {'open': None, 'history': []}
            if os.path.exists(DATA_CACHE_FILE):
                try:
                    with open(DATA_CACHE_FILE, 'rb') as f:
                        cache = pickle.load(f)
                except Exception as e:
                    logging.error(f"加载缓存失败: {e}")

            # 从缓存中提取不同时间点的数据
            cache_open = cache['open']  # 开盘数据
            cache_15min = get_data_from_cache(cache, 15)  # 15分钟前
            cache_30min = get_data_from_cache(cache, 30)  # 30分钟前
            cache_60min = get_data_from_cache(cache, 60)  # 60分钟前
            cache_latest = get_data_from_cache(cache, 0)  # 最近一次（上次运行）

            # 模块二：生成动态资金流报告
            dynamic_stock_report = generate_dynamic_fund_flow_report(
                fund_flow_df, cache_open, cache_15min, cache_30min, cache_60min, cache_latest
            )

            # 【新增】生成行业概念动态报告
            dynamic_sector_concept_report = generate_dynamic_sector_concept_report(
                sector_fund_flow_df,
                concept_fund_flow_df,
                cache_open,
                cache_15min,
                cache_30min,
                cache_60min,
                cache_latest,
                fund_flow_df  # 传入个股数据用于交叉验证
            )

            # 读取背景资料和昨日总结（保持原有功能）
            book_context = read_book_files()
            previous_day_summary = ""

            # 在job()中准备所有Prompt变量
            top_n = 200
            # 【新增】格式化个股资金流数据显示
            if fund_flow_df is not None and not fund_flow_df.empty:
                fund_flow_formatted = format_dataframe_for_display(fund_flow_df)
                static_stock_data_str = fund_flow_formatted.head(top_n).to_string(index=False)
            else:
                static_stock_data_str = "无个股资金流数据"
            
            # 【新增】格式化行业资金流数据显示
            if sector_fund_flow_df is not None and not sector_fund_flow_df.empty:
                sector_formatted = format_dataframe_for_display(sector_fund_flow_df)
                static_sector_data_str = sector_formatted.head(50).to_string(index=False)
            else:
                static_sector_data_str = "无行业资金流数据"
            
            # 处理概念数据，确保过滤掉code列并格式化数值显示
            if concept_fund_flow_df is not None and not concept_fund_flow_df.empty:
                concept_for_ai = concept_fund_flow_df.copy()
                # 再次确保过滤掉code列（防止某些数据源没有过滤）
                if 'code' in concept_for_ai.columns:
                    concept_for_ai = concept_for_ai.drop('code', axis=1)
                # 【新增】格式化数值显示，解决科学计数法问题
                concept_for_ai_formatted = format_dataframe_for_display(concept_for_ai)
                static_concept_data_str = concept_for_ai_formatted.head(50).to_string(index=False)
            else:
                static_concept_data_str = "无概念资金流数据"
            # 【新增】格式化涨停股池数据显示并添加统计分析
            if zt_pool_df is not None and not zt_pool_df.empty:
                # 生成统计分析
                zt_statistics = analyze_zt_pool_statistics(zt_pool_df)
                
                # 格式化数据显示
                zt_pool_formatted = format_dataframe_for_display(zt_pool_df)
                data_display = zt_pool_formatted.head(top_n).to_string(index=False)
                
                # 如果有统计信息，将其添加到数据上方
                if zt_statistics.strip():
                    static_zt_pool_data_str = zt_statistics + "\n" + data_display
                else:
                    static_zt_pool_data_str = data_display
            else:
                static_zt_pool_data_str = "无涨停股池数据"
            # 【新增】格式化同花顺个股资金流数据显示
            if ths_fund_flow_df is not None and not ths_fund_flow_df.empty:
                ths_fund_flow_formatted = format_dataframe_for_display(ths_fund_flow_df)
                static_ths_fund_flow_data_str = ths_fund_flow_formatted.head(top_n).to_string(index=False)
            else:
                static_ths_fund_flow_data_str = "无同花顺个股资金流数据"
            static_ths_big_deal_data_str = analyze_ths_big_deal_data(ths_big_deal_df, top_n) if ths_big_deal_df is not None else "无同花顺大单追踪数据"
            # 【新增】格式化板块异动详情数据显示
            if board_changes_df is not None and not board_changes_df.empty:
                board_changes_formatted = format_dataframe_for_display(board_changes_df)
                static_board_changes_data_str = board_changes_formatted.head(top_n).to_string(index=False)
            else:
                static_board_changes_data_str = "无板块异动详情数据"
            static_market_movers_data_str = market_movers_data if market_movers_data else "无盘口异动数据"

            # 【新增】处理技术指标数据并格式化显示
            tech_indicators_text_list = []
            if tech_indicators_dict:
                for name, df in tech_indicators_dict.items():
                    if df is not None and not df.empty:
                        # 格式化技术指标数据
                        tech_formatted = format_dataframe_for_display(df)
                        tech_indicators_text_list.append(f"\n--- {name} ---\n{tech_formatted.head(30).to_string(index=False)}")
            static_tech_indicators_data_str = "\n".join(tech_indicators_text_list) if tech_indicators_text_list else "无技术指标数据"

            news_data_str = news_data if news_data else "无新闻资讯数据"

            # 模块三：调用重构后的analyze_with_gemini函数
            analyze_with_gemini(
                dynamic_stock_report,
                dynamic_sector_concept_report,  # <--- 传入新报告
                news_data_str,
                static_stock_data_str,
                static_sector_data_str,
                static_concept_data_str,
                static_zt_pool_data_str,
                static_board_changes_data_str,
                static_market_movers_data_str,
                static_tech_indicators_data_str,
                static_ths_fund_flow_data_str,
                static_ths_big_deal_data_str
            )

            # 模块一：在job()执行末尾管理缓存
            manage_cache(new_data_dict)

        print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 任务执行完成")

    except Exception as e:
        logging.error(f"任务执行过程中发生错误: {e}")
    finally:
        # 无论任务成功还是失败，都要重置任务状态
        TASK_RUNNING = False
# ==============================================================================
# 5. 主程序入口
# ==============================================================================
if __name__ == "__main__":
    # 执行一次盘中任务
    job()
    # 每隔指定分钟执行一次盘中任务
    schedule.every(FETCH_INTERVAL_MINUTES).minutes.do(job)

    print(f"✅ 初始化完成。盘中任务将每 {FETCH_INTERVAL_MINUTES} 分钟运行一次。")

    print("按 Ctrl+C 退出程序。")

    try:
        while True:
            schedule.run_pending()
            time_module.sleep(1)
    except KeyboardInterrupt:
        print("\n程序已退出。")