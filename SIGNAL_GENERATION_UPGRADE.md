# Gemini_v2.py 信号生成功能升级报告

## 概述

本次升级重构了 `Gemini_v2.py` 中的信号生成逻辑，从简单的股票代码提取升级为完整的JSON格式信号文件生成，支持解析AI模型生成的"最终交易计划汇总"或"模拟买入计划"表格。

## 主要修改内容

### 1. 函数重构

#### `write_signal_file_atomically` 函数升级
- **原功能**: 只支持写入股票代码列表到EBK文件
- **新功能**: 
  - 支持JSON和EBK两种文件格式
  - 自动根据文件扩展名判断格式
  - JSON文件使用UTF-8编码，EBK文件保持GBK编码兼容性
  - 支持Python字典/列表数据结构的JSON序列化

#### `parse_and_save_gemini_watchlist` 函数重构
- **原功能**: 简单提取6位数字股票代码
- **新功能**: 
  - 解析AI响应中的Markdown表格
  - 提取股票代码、名称、买入金额、入选理由
  - 生成结构化的JSON信号数据
  - 同时保持EBK文件兼容性

### 2. 新增辅助函数

#### `parse_trading_plan_table(response_text)`
- 核心表格解析函数
- 支持多种表格标题识别：
  - "最终交易计划汇总"
  - "模拟买入计划"
  - "模拟卖出计划"
  - "交易计划汇总"
- 使用正则表达式精确解析表格行
- 支持标准格式和宽松格式两种解析模式

#### `parse_amount(amount_str)`
- 解析金额字符串，支持逗号分隔符
- 返回整数金额值

#### `infer_signal_level(reason, amount_str)`
- 根据理由关键词和金额推断信号等级
- 支持AF（攻坚部队）、MBG（主战军团）、SO（潜伏哨）三个等级
- 智能关键词匹配和金额阈值判断

#### `extract_stocks_from_text(response_text)`
- 备用解析方法
- 当表格解析失败时，从文本中提取股票代码
- 生成基本的信号结构

### 3. JSON数据结构

生成的JSON文件结构如下：

```json
{
  "version": "1.0",
  "timestamp": "2025-08-06 10:30:05",
  "signals": [
    {
      "code": "600519",
      "type": "buy",
      "level": "AF",
      "reason": "冠军对决胜者，市场最强主线核心",
      "plan_amount": 150000
    }
  ]
}
```

#### 字段说明：
- `version`: 数据格式版本号
- `timestamp`: 信号生成时间戳
- `signals`: 信号列表
  - `code`: 股票代码（6位数字）
  - `type`: 信号类型（buy/sell）
  - `level`: 作战单元等级（AF/MBG/SO）
  - `reason`: 入选核心理由
  - `plan_amount`: 计划买入金额

### 4. 文件输出

#### 主要输出文件
- **D:/gemini_signal.json**: 主要的JSON格式信号文件
- **D:/gemini_signal.ebk**: 兼容性EBK文件（仅包含买入股票代码）

#### 兼容性保证
- 保持原有EBK文件格式不变
- 同时生成新的JSON格式文件
- 现有系统可以继续使用EBK文件
- 新系统可以使用功能更丰富的JSON文件

### 5. 错误处理和日志

#### 增强的错误处理
- 表格解析失败时自动降级到文本提取
- 无信号时生成空的JSON结构
- 完整的异常捕获和日志记录

#### 详细的日志输出
- 表格识别和解析过程日志
- 信号提取和验证日志
- 文件写入操作日志
- 兼容性文件生成日志

### 6. 正则表达式优化

#### 表格解析正则
```python
# 标准表格格式
table_pattern = r'([A-Z]{1,3})\s*[|\t]\s*(\d{6})\s*[|\t]\s*([^\t|]+?)\s*[|\t]\s*([\d,]+)\s*[|\t]\s*([^\n\r|]+)'

# 宽松格式（备用）
line_pattern = r'.*?(\d{6}).*?([^\d\s][^\t|]*?)\s*[|\t]\s*([\d,]+).*?([^\n\r]{10,})'
```

#### 字符清理
- 自动清理理由字段末尾的"|"字符
- 限制理由长度为100字符
- 去除多余空格和特殊字符

## 测试验证

### 测试用例
创建了完整的测试脚本验证以下功能：
1. 表格解析准确性
2. JSON文件生成正确性
3. EBK文件兼容性
4. 错误处理机制

### 测试结果
- ✅ 表格解析：成功解析5条信号
- ✅ JSON生成：格式正确，编码无误
- ✅ 数据完整性：所有字段正确提取
- ✅ 兼容性：EBK文件正常生成

## 使用说明

### 调用方式
```python
# 原有调用方式（已更新）
parse_and_save_gemini_watchlist(response.text, "D:/gemini_signal.json")
```

### AI Prompt要求
为了确保解析成功，AI响应应包含以下格式的表格：

```markdown
### 模拟买入计划

| 归属单元 | 股票代码 | 股票名称 | 买入金额(元) | 入选核心理由 |
|---------|---------|---------|-------------|-------------|
| AF | 600519 | 贵州茅台 | 150,000 | 冠军对决胜者，市场最强主线核心 |
| MBG | 000001 | 平安银行 | 83,300 | 主线核心中军 |
```

### 输出文件
- 主文件：`D:/gemini_signal.json`
- 兼容文件：`D:/gemini_signal.ebk`

## 技术特点

### 最小化修改原则
- 保持原有函数接口不变
- 添加新功能而不破坏现有逻辑
- 向后兼容性完全保证

### 健壮性设计
- 多层次解析策略
- 优雅的降级处理
- 完整的错误恢复机制

### 扩展性考虑
- 支持未来添加卖出信号
- 可扩展更多信号类型
- 灵活的数据结构设计

## 总结

本次升级成功实现了从简单代码提取到完整信号解析的跃升，为量化交易系统提供了更丰富、更结构化的信号数据，同时保持了完全的向后兼容性。新的JSON格式为未来的功能扩展和系统集成提供了坚实的基础。
